# reporters/csvfile_reporter.py
import csv
from typing import Dict, Any
from reporters.base_reporter import BaseReporter
from core.models import DiffResult

class CsvFileReporter(BaseReporter):
    """将差异报告写入 CSV 文件的报告器。"""

    def __init__(self, config: Dict[str, Any]):
        self.filepath = config.get('filepath', 'diff_results.csv')
        self.file_handle = None
        self.writer = None
        # 批量写入缓冲区优化
        self.buffer = []
        self.buffer_size = 10000

    def open(self):
        """打开 CSV 文件并写入表头。"""
        try:
            self.file_handle = open(self.filepath, 'w', newline='', encoding='utf-8')
            self.writer = csv.writer(self.file_handle)
            # 写入表头
            self.writer.writerow(['TABLE_NAME', 'KEY', 'STATUS', 'VALUE_A', 'VALUE_B'])
        except IOError as e:
            print(f"无法打开报告文件 {self.filepath}: {e}")
            raise

    def close(self):
        """关闭 CSV 文件。"""
        # 写入剩余缓冲区数据
        self._flush_buffer()
        if self.file_handle:
            self.file_handle.close()
            print(f"报告文件已关闭: {self.filepath}")

    def report_diff(self, diff_result: Any, table_name: str = None):
        """将一条差异记录添加到缓冲区"""
        if not self.writer:
            raise IOError("报告文件未打开或写入器不可用。")

        # 处理输入数据格式
        if isinstance(diff_result, dict):
            diff_dict = DiffResult(
                key=diff_result.get('record_key', ''),
                status=diff_result.get('status', ''),
                value_a=diff_result.get('source_value'),
                value_b=diff_result.get('target_value')
            )
            table_name = diff_result.get('table_name', table_name or '')
        else:
            diff_dict = diff_result

        # 添加到缓冲区
        self.buffer.append([
            table_name,
            diff_dict.key,
            diff_dict.status,
            diff_dict.value_a,
            diff_dict.value_b
        ])
        
        # 缓冲区满时批量写入
        if len(self.buffer) >= self.buffer_size:
            self._flush_buffer()
    
    def _flush_buffer(self):
        """将缓冲区数据批量写入文件。"""
        if self.buffer and self.writer:
            self.writer.writerows(self.buffer)
            self.file_handle.flush()  # 强制刷新到磁盘
            self.buffer.clear()