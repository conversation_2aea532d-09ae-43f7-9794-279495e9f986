#!/usr/bin/env python3
"""
Cython模块调用修复验证脚本
==========================

验证修复后的engine_hybrid.py中的Cython模块调用是否正常工作：
1. 诊断Cython模块状态
2. 测试模块导入和函数调用
3. 验证错误处理机制
4. 测试完整调用链路
"""

import os
import sys
import logging

# 配置日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def print_header():
    """打印标题"""
    print("=" * 70)
    print("Cython模块调用修复验证")
    print("=" * 70)

def test_cython_diagnosis():
    """测试Cython诊断功能"""
    print("\n🔍 [1/5] Cython模块诊断...")
    
    try:
        from core.engine_hybrid import diagnose_cython_module
        diagnosis = diagnose_cython_module()
        
        print("📊 诊断结果:")
        print(f"  源文件存在: {diagnosis['source_file_exists']}")
        print(f"  编译产物: {len(diagnosis['compiled_files'])} 个")
        
        for file in diagnosis['compiled_files']:
            size = os.path.getsize(file) if os.path.exists(file) else 0
            print(f"    📄 {file} ({size:,} bytes)")
        
        print(f"  导入尝试: {len(diagnosis['import_attempts'])} 次")
        for attempt in diagnosis['import_attempts']:
            status = "✅ 成功" if attempt['success'] else "❌ 失败"
            print(f"    {attempt['path']}: {status}")
            if not attempt['success']:
                print(f"      错误: {attempt['error']}")
            else:
                print(f"      属性数: {len(attempt.get('attributes', []))}")
        
        print(f"  关键函数: {diagnosis['module_attributes']}")
        
        return len(diagnosis['compiled_files']) > 0 and any(a['success'] for a in diagnosis['import_attempts'])
        
    except Exception as e:
        print(f"❌ 诊断失败: {e}")
        return False

def test_cython_availability():
    """测试Cython可用性检查"""
    print("\n🔧 [2/5] 测试Cython可用性检查...")
    
    try:
        from core.engine_hybrid import _check_cython_availability
        
        # 重置状态以强制重新检查
        import core.engine_hybrid
        core.engine_hybrid._cython_available = None
        core.engine_hybrid._cython_module = None
        
        available = _check_cython_availability()
        
        print(f"  Cython可用性: {'✅ 可用' if available else '❌ 不可用'}")
        
        if available:
            # 验证模块和函数
            module = core.engine_hybrid._cython_module
            if module:
                functions = ['compare_sources_cython', 'compare_sources_memory_dict_cython']
                for func_name in functions:
                    has_func = hasattr(module, func_name)
                    print(f"  函数 {func_name}: {'✅ 存在' if has_func else '❌ 缺失'}")
        
        return available
        
    except Exception as e:
        print(f"❌ 可用性检查失败: {e}")
        return False

def test_hybrid_functions():
    """测试混合引擎函数"""
    print("\n⚡ [3/5] 测试混合引擎函数...")
    
    # 创建模拟连接器
    class MockRecord:
        def __init__(self, key, value):
            self.key = key
            self.value = value

    class MockConnector:
        def __init__(self, data):
            self.data = data
            self.index = 0
        
        def __enter__(self):
            self.index = 0
            return self
        
        def __exit__(self, exc_type, exc_val, exc_tb):
            pass
        
        def __iter__(self):
            self.index = 0
            return self
        
        def __next__(self):
            if self.index >= len(self.data):
                raise StopIteration
            key, value = self.data[self.index]
            self.index += 1
            return MockRecord(key, value)
    
    try:
        from core.engine_hybrid import compare_sources_hybrid, compare_sources_memory_dict_hybrid
        
        # 创建测试数据
        test_data_a = [("key1", {"id": 1, "name": "test1"}), ("key2", {"id": 2, "name": "test2"})]
        test_data_b = [("key1", {"id": 1, "name": "test1"}), ("key2", {"id": 2, "name": "modified"})]
        
        source_a = MockConnector(test_data_a)
        source_b = MockConnector(test_data_b)
        
        # 测试流式比对
        print("  测试流式比对...")
        result1 = compare_sources_hybrid(source_a, source_b, None)
        print(f"    ✅ 流式比对成功: 处理 {result1['processed_records']} 条记录")
        
        # 重新创建连接器（因为已被消耗）
        source_a = MockConnector(test_data_a)
        source_b = MockConnector(test_data_b)
        
        # 测试内存字典比对（如果支持）
        print("  测试内存字典比对...")
        try:
            # 为内存字典算法创建特殊的连接器
            class MockMemoryConnector:
                def __init__(self, data):
                    self.data = data
                
                def __enter__(self):
                    return self
                
                def __exit__(self, exc_type, exc_val, exc_tb):
                    pass
                
                def fetch_all_data(self, shared_dict=None, data_source='t1'):
                    if shared_dict is None:
                        shared_dict = {}
                    
                    for key, value in self.data:
                        if key not in shared_dict:
                            shared_dict[key] = {'t1': '', 't2': ''}
                        shared_dict[key][data_source] = value
                    
                    return shared_dict
            
            mem_source_a = MockMemoryConnector(test_data_a)
            mem_source_b = MockMemoryConnector(test_data_b)
            
            result2 = compare_sources_memory_dict_hybrid(mem_source_a, mem_source_b, None)
            print(f"    ✅ 内存字典比对成功: 处理 {result2['processed_records']} 条记录")
        except Exception as e:
            print(f"    ⚠️  内存字典比对跳过: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 混合引擎测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_error_handling():
    """测试错误处理机制"""
    print("\n🛡️ [4/5] 测试错误处理机制...")
    
    try:
        from core.engine_hybrid import compare_sources_hybrid
        
        # 测试无效输入
        print("  测试无效输入处理...")
        try:
            result = compare_sources_hybrid(None, None, None)
            print("    ⚠️  应该抛出异常但没有")
            return False
        except Exception as e:
            print(f"    ✅ 正确处理无效输入: {type(e).__name__}")
        
        # 测试强制Python模式
        print("  测试强制Python模式...")
        class MockConnector:
            def __enter__(self): return self
            def __exit__(self, *args): pass
            def __iter__(self): return iter([])
        
        try:
            result = compare_sources_hybrid(MockConnector(), MockConnector(), None, force_python=True)
            print("    ✅ 强制Python模式工作正常")
        except Exception as e:
            print(f"    ⚠️  强制Python模式异常: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 错误处理测试失败: {e}")
        return False

def test_full_call_chain():
    """测试完整调用链路"""
    print("\n🔗 [5/5] 测试完整调用链路...")
    
    try:
        # 测试从main_v4.py的调用链路
        print("  测试ComparisonTaskOrchestrator导入...")
        from main_v4 import ComparisonTaskOrchestrator
        print("    ✅ ComparisonTaskOrchestrator导入成功")
        
        print("  测试创建实例...")
        orchestrator = ComparisonTaskOrchestrator(use_cython=True)
        print("    ✅ 实例创建成功")
        
        print("  测试引擎信息...")
        from core.engine_hybrid import get_engine_info
        info = get_engine_info()
        print(f"    推荐引擎: {info.get('recommended_engine', 'unknown')}")
        print(f"    Cython可用: {info.get('cython_available', False)}")
        print(f"    性能提升: {info.get('performance_boost', 'unknown')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 完整调用链路测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print_header()
    
    tests = [
        ("Cython模块诊断", test_cython_diagnosis),
        ("Cython可用性检查", test_cython_availability),
        ("混合引擎函数测试", test_hybrid_functions),
        ("错误处理机制", test_error_handling),
        ("完整调用链路", test_full_call_chain),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for i, (test_name, test_func) in enumerate(tests, 1):
        print(f"\n{'='*20} [{i}/{total_tests}] {test_name} {'='*20}")
        
        try:
            if test_func():
                passed_tests += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    # 总结
    print(f"\n{'='*70}")
    print(f"修复验证结果: {passed_tests}/{total_tests} 测试通过")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过! Cython模块调用修复成功")
        print("\n✅ 修复效果:")
        print("  - 增强的模块导入逻辑")
        print("  - 详细的错误诊断")
        print("  - 健壮的函数验证")
        print("  - 完整的调用链路")
    elif passed_tests >= total_tests * 0.6:
        print("⚠️  大部分测试通过，修复基本成功")
        print("💡 建议检查失败的测试项")
    else:
        print("❌ 多个测试失败，需要进一步修复")
    
    print("=" * 70)
    
    return passed_tests >= total_tests * 0.8

if __name__ == "__main__":
    try:
        success = main()
        input("\n按Enter键退出...")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
