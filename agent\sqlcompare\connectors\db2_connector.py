# connectors/db2_connector.py
import os
import sys
import time
import threading
from typing import Iterator, Dict, Any, List
from collections import deque
import logging

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, SCRIPT_DIR)
sys.path.insert(0, os.path.dirname(SCRIPT_DIR))
from core.models import Record
from base_connector import BaseConnector
from utils.imdb_manager import configure_db2_driver
import ibm_db

logger = logging.getLogger(__name__)


class DB2ConnectionPool:
    """DB2连接池管理器 - 单例模式，线程安全"""
    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        if self._initialized:
            return

        self._pools = {}
        self._pool_lock = threading.Lock()
        self._max_pool_size = 3
        self._connection_timeout = 5
        self._max_idle_time = 180
        self._cleanup_interval = 60
        self._last_cleanup = time.time()
        self._initialized = True

    def _get_connection_key(self, db_config: Dict[str, Any]) -> str:
        """生成连接池键值"""
        return f"{db_config['ip']}:{db_config['port']}:{db_config['schema']}:{db_config['user_name']}"

    def _create_connection_string(self, db_config: Dict[str, Any]) -> str:
        """创建优化的DB2连接字符串"""
        # 添加更多DB2性能优化参数
        conn_str = (
            f"DATABASE={db_config['schema']};"
            f"HOSTNAME={db_config['ip']};"
            f"PORT={db_config['port']};"
            f"PROTOCOL=TCPIP;"
            f"UID={db_config['user_name']};"
            f"PWD={db_config['password']};"
            f"AUTHENTICATION=SERVER;"
            f"CONNECTTIMEOUT={self._connection_timeout};"
        )

        current_schema = db_config.get('current_schema')
        if current_schema and current_schema != db_config['schema']:
            conn_str += f"CURRENTSCHEMA={current_schema};"

        return conn_str

    def _create_new_connection(self, db_config: Dict[str, Any]) -> Any:
        """创建新的DB2连接"""
        try:
            conn_str = self._create_connection_string(db_config)
            conn = ibm_db.connect(conn_str, "", "")

            if not conn:
                raise Exception("连接对象为空")

            try:
                # 使用更安全的方式设置连接选项
                ibm_db.autocommit(conn, ibm_db.SQL_AUTOCOMMIT_OFF)
            except Exception as opt_error:
                logger.warning(f"设置连接选项失败，但连接仍可用: {opt_error}")

            return conn

        except Exception as e:
            logger.error(f"创建DB2连接失败: {e}")
            raise

    def get_connection(self, db_config: Dict[str, Any]) -> Any:
        """从连接池获取连接"""
        key = self._get_connection_key(db_config)

        with self._pool_lock:
            self._cleanup_idle_connections()

            # 获取或创建连接池
            if key not in self._pools:
                self._pools[key] = {
                    'connections': deque(),
                    'config': db_config.copy(),
                    'created_count': 0
                }

            pool = self._pools[key]

            # 尝试从池中获取可用连接
            while pool['connections']:
                conn_info = pool['connections'].popleft()
                conn = conn_info['connection']

                # 检查连接是否仍然有效
                if self._is_connection_valid(conn):
                    conn_info['last_used'] = time.time()
                    return conn
                else:
                    self._close_connection_safely(conn)

            # 池中没有可用连接，创建新连接
            if pool['created_count'] < self._max_pool_size:
                conn = self._create_new_connection(db_config)
                pool['created_count'] += 1
                return conn
            else:
                # 达到最大连接数，等待或抛出异常
                raise Exception(f"DB2连接池已满，无法创建更多连接: {key}")

    def return_connection(self, db_config: Dict[str, Any], conn: Any):
        """将连接返回到连接池"""
        if not conn:
            return

        key = self._get_connection_key(db_config)

        with self._pool_lock:
            if key in self._pools:
                pool = self._pools[key]

                # 检查连接是否仍然有效
                if self._is_connection_valid(conn):
                    conn_info = {
                        'connection': conn,
                        'last_used': time.time(),
                        'created_at': time.time()
                    }
                    pool['connections'].append(conn_info)
                else:
                    # 连接无效，关闭并减少计数
                    self._close_connection_safely(conn)
                    pool['created_count'] = max(0, pool['created_count'] - 1)

    def _is_connection_valid(self, conn: Any) -> bool:
        """检查DB2连接是否有效"""
        try:
            # 执行简单查询测试连接
            stmt = ibm_db.exec_immediate(conn, "SELECT 1 FROM SYSIBM.SYSDUMMY1")
            if stmt:
                ibm_db.free_result(stmt)
                return True
            return False
        except:
            return False

    def _close_connection_safely(self, conn: Any):
        """安全关闭DB2连接"""
        try:
            if conn:
                ibm_db.close(conn)
        except:
            pass  # 忽略关闭时的异常

    def _cleanup_idle_connections(self):
        """清理空闲连接"""
        current_time = time.time()

        # 检查是否需要清理
        if current_time - self._last_cleanup < self._cleanup_interval:
            return

        self._last_cleanup = current_time

        for key, pool in list(self._pools.items()):
            connections_to_remove = []

            # 检查每个连接的空闲时间
            for i, conn_info in enumerate(pool['connections']):
                if current_time - conn_info['last_used'] > self._max_idle_time:
                    connections_to_remove.append(i)

            # 移除空闲连接
            for i in reversed(connections_to_remove):
                conn_info = pool['connections'][i]
                self._close_connection_safely(conn_info['connection'])
                del pool['connections'][i]
                pool['created_count'] = max(0, pool['created_count'] - 1)

    def close_all_connections(self):
        """关闭所有连接池中的连接"""
        with self._pool_lock:
            for key, pool in self._pools.items():
                while pool['connections']:
                    conn_info = pool['connections'].popleft()
                    self._close_connection_safely(conn_info['connection'])
                pool['created_count'] = 0
            self._pools.clear()


# 全局连接池实例
_connection_pool = None
_pool_init_lock = threading.Lock()


def get_connection_pool() -> DB2ConnectionPool:
    """获取全局连接池实例"""
    global _connection_pool
    if _connection_pool is None:
        with _pool_init_lock:
            if _connection_pool is None:
                _connection_pool = DB2ConnectionPool()
    return _connection_pool


class DB2Connector(BaseConnector):
    """优化的DB2数据库连接器 - 支持连接池、批量优化和内存管理"""

    # 类级别的配置缓存，避免重复配置检查
    _driver_configured = False
    _config_lock = threading.Lock()

    def __init__(self, db_config: Dict[str, Any], query: str = "", batch_size: int = 10000):
        self.db_config = db_config.copy()  # 防止外部修改
        self.query = query
        self.batch_size = max(1000, min(batch_size, 50000))
        self.conn = None
        self._iterator = None
        self._use_connection_pool = True
        self._connection_pool = get_connection_pool()

        # 性能统计
        self._stats = {
            'total_fetched': 0,
            'fetch_time': 0.0,
            'connection_time': 0.0
        }

        # 优化的查询缓存
        self.query_cache = {}

    def set_table_query(self, table_name: str, query: str):
        """设置或更新查询语句"""
        self.query = query
        self.table_name = table_name
        # 清空查询缓存
        self.query_cache.clear()
        # 重置迭代器
        self._iterator = None

    def get_table_name(self) -> str:
        """获取当前查询表名"""
        return self.table_name

    @classmethod
    def _ensure_driver_configured(cls):
        """确保DB2驱动已配置（类级别缓存，避免重复配置）"""
        if not cls._driver_configured:
            with cls._config_lock:
                if not cls._driver_configured:
                    configure_db2_driver()
                    cls._driver_configured = True

    def connect(self):
        """建立到 DB2 数据库的连接（使用连接池）"""
        start_time = time.perf_counter()

        try:
            if self._use_connection_pool:
                self.conn = self._connection_pool.get_connection(self.db_config)
            else:
                # 回退到直接连接
                self.conn = self._create_direct_connection()

            self._stats['connection_time'] += time.perf_counter() - start_time

        except Exception as e:
            logger.error(f"DB2数据库连接失败: {e}")
            # 尝试回退到直接连接
            if self._use_connection_pool:
                logger.warning("连接池连接失败，尝试直接连接")
                self._use_connection_pool = False
                self.conn = self._create_direct_connection()
            else:
                raise

    def _create_direct_connection(self):
        """创建直接DB2连接（不使用连接池）"""
        try:
            # 验证必要的连接参数
            required_params = ['schema', 'ip', 'port', 'user_name', 'password']
            missing_params = [param for param in required_params if not self.db_config.get(param)]

            if missing_params:
                raise ValueError(f"缺少必要的连接参数: {missing_params}")

            # 构建连接字符串
            conn_str = (
                f"DATABASE={self.db_config['schema']};"
                f"HOSTNAME={self.db_config['ip']};"
                f"PORT={self.db_config['port']};"
                f"PROTOCOL=TCPIP;"
                f"UID={self.db_config['user_name']};"
                f"PWD={self.db_config['password']};"
                f"AUTHENTICATION=SERVER;"
                f"CONNECTTIMEOUT=30;"
            )

            conn = ibm_db.connect(conn_str, "", "")

            if not conn:
                raise Exception("直接连接创建失败，连接对象为空")

            logger.debug("创建直接DB2连接成功")
            return conn

        except Exception as e:
            logger.error(f"创建直接DB2连接失败: {e}")
            raise

    def close(self):
        """关闭数据库连接（返回到连接池或直接关闭）"""
        if self.conn:
            try:
                if self._use_connection_pool:
                    self._connection_pool.return_connection(self.db_config, self.conn)
                else:
                    ibm_db.close(self.conn)
            except Exception as e:
                logger.warning(f"关闭DB2连接时出现警告: {e}")
            finally:
                self.conn = None

    def get_record_count(self) -> int:
        """
        获取查询结果的精确记录数量（优化版本）

        Returns:
            记录数量
        """
        if not self.conn:
            raise ConnectionError("数据库未连接。")

        try:
            # 使用缓存的优化查询
            count_query = self._get_optimized_count_query()

            # 执行COUNT查询，添加性能提示
            start_time = time.perf_counter()
            stmt = ibm_db.exec_immediate(self.conn, count_query)

            if not stmt:
                raise Exception("COUNT查询执行失败")

            row = ibm_db.fetch_tuple(stmt)
            ibm_db.free_result(stmt)  # 及时释放结果集

            query_time = time.perf_counter() - start_time

            if row and len(row) > 0:
                count = int(row[0])
                logger.debug(f"COUNT查询完成，记录数: {count}, 耗时: {query_time:.3f}s")
                return count
            else:
                raise Exception("COUNT查询返回空结果")

        except Exception as e:
            logger.error(f"COUNT查询失败: {e}")
            raise

    def _get_optimized_count_query(self) -> str:
        """获取优化的COUNT查询（带缓存）"""
        cache_key = f"count_{hash(self.query)}"

        if cache_key in self.query_cache:
            return self.query_cache[cache_key]

        # 构造优化的COUNT查询
        original_query = self.query.strip()
        query_upper = original_query.upper()

        # 移除ORDER BY子句（COUNT查询不需要排序）
        order_by_pos = query_upper.rfind("ORDER BY")
        if order_by_pos != -1:
            base_query = original_query[:order_by_pos].strip()
        else:
            base_query = original_query

        # 移除FETCH FIRST限制（COUNT查询需要统计全部记录）
        fetch_first_pos = query_upper.rfind("FETCH FIRST")
        if fetch_first_pos != -1:
            base_query = original_query[:fetch_first_pos].strip()

        # 移除WITH UR等查询提示（COUNT查询不需要）
        with_ur_pos = query_upper.rfind("WITH UR")
        if with_ur_pos != -1:
            base_query = original_query[:with_ur_pos].strip()

        # 构造优化的COUNT查询，添加DB2性能提示
        count_query = f"SELECT COUNT(*) FROM ({base_query}) AS count_subquery WITH UR"

        # 缓存查询
        self.query_cache[cache_key] = count_query

        return count_query

    def fetch_data(self) -> Iterator[Record]:
        """
        从DB2数据库获取数据，返回优化的迭代器
        """
        if not self.conn:
            raise ConnectionError("数据库未连接。")

        # 获取优化的查询（带缓存）
        optimized_query = self._get_streaming_query()

        # 使用优化的流式处理
        yield from self._fetch_streaming_data(optimized_query)

    def _get_streaming_query(self) -> str:
        """获取优化的数据获取查询（带缓存）"""
        cache_key = f"fetch_{hash(self.query)}"

        if cache_key in self.query_cache:
            return self.query_cache[cache_key]

        # 智能处理ORDER BY子句，确保数据按KEY排序
        query_upper = self.query.upper()
        order_by_pos = query_upper.rfind("ORDER BY")

        if order_by_pos != -1:
            # 如果用户提供了显式的ORDER BY，则使用它
            order_by_clause = self.query[order_by_pos:]
            base_query = self.query[:order_by_pos].strip()
        else:
            # 没有ORDER BY时，自动按第一列排序
            order_by_clause = "ORDER BY 1"
            base_query = self.query.strip()

        # 添加DB2性能优化提示
        optimized_query = f"{base_query} {order_by_clause} WITH UR FOR READ ONLY"

        # 缓存优化查询
        self.query_cache[cache_key] = optimized_query

        return optimized_query

    def _fetch_streaming_data(self, optimized_query: str) -> Iterator[Record]:
        """极致优化的流式读取：最大化性能和内存效率"""
        stmt = None
        try:
            start_time = time.perf_counter()

            # 执行查询 - 这是真正的瓶颈所在
            stmt = ibm_db.exec_immediate(self.conn, optimized_query)
            query_exec_time = time.perf_counter() - start_time
            logger.info(f"查询执行完成: {query_exec_time:.3f}s - {self.db_config['ip']}")

            if not stmt:
                raise Exception("查询执行失败")

            # 预计算列信息，避免重复调用
            num_fields = ibm_db.num_fields(stmt)
            if num_fields == 0:
                logger.warning("查询返回0个字段")
                return

            # 优化列信息获取
            columns = tuple(ibm_db.field_name(stmt, i) for i in range(num_fields))
            value_columns = columns[1:] if len(columns) > 1 else ()
            value_columns_len = len(value_columns)

            # 预分配常用变量，减少查找开销
            str_type = str
            isinstance_func = isinstance
            Record_class = Record

            batch_count = 0
            total_rows = 0

            while True:
                # 批量获取多行数据
                batch_start = time.perf_counter()
                batch_rows = ibm_db.fetchmany(stmt, self.batch_size)

                if not batch_rows:
                    break

                batch_count += 1
                batch_size = len(batch_rows)
                total_rows += batch_size

                # 优化的批量处理 - 减少函数调用和对象创建
                if value_columns_len == 0:
                    # 只有key列的情况
                    for row_tuple in batch_rows:
                        key = row_tuple[0] if isinstance_func(row_tuple[0], str_type) else str_type(row_tuple[0])
                        yield Record_class(key=key, value={})
                else:
                    # 有value列的情况 - 使用更高效的字典构建
                    for row_tuple in batch_rows:
                        # 优化key处理
                        key = row_tuple[0] if isinstance_func(row_tuple[0], str_type) else str_type(row_tuple[0])

                        # 优化值字典构建 - 避免zip()的开销
                        if value_columns_len == 1:
                            # 单列优化
                            value = {value_columns[0]: row_tuple[1]}
                        elif value_columns_len <= 5:
                            # 少量列直接构建
                            value = {value_columns[i]: row_tuple[i + 1] for i in range(value_columns_len)}
                        else:
                            # 多列使用zip（仍然比原来的dict(zip())快）
                            value = dict(zip(value_columns, row_tuple[1:]))

                        yield Record_class(key=key, value=value)

            # 更新统计信息
            total_time = time.perf_counter() - start_time
            self._stats['total_fetched'] += total_rows
            self._stats['fetch_time'] += total_time

            logger.info(f"流式读取完成: {total_rows} 行, {batch_count} 批次, 总耗时: {total_time:.3f}s")

        except Exception as e:
            logger.error(f"优化流式查询失败: {e}")
            raise
        finally:
            # 确保释放资源
            if stmt:
                try:
                    ibm_db.free_result(stmt)
                except:
                    pass

    def __iter__(self) -> Iterator[Record]:
        """返回数据迭代器"""
        self._iterator = self.fetch_data()
        return self._iterator

    def __next__(self) -> Record:
        """获取下一条记录"""
        if self._iterator is None:
            self._iterator = self.fetch_data()
        try:
            return next(self._iterator)
        except StopIteration:
            raise

    def fetch_all_data(self, shared_dict: Dict[str, Dict[str, str]] = None, data_source: str = 't1') -> Dict[str, Dict[str, str]]:
        """
        一次性获取所有数据到内存字典

        Args:
            shared_dict: 共享的内存字典，如果提供则直接更新此字典
            data_source: 数据源标识，'t1'表示第一个数据源，'t2'表示第二个数据源

        Returns:
            字典格式: {key: {'t1': str_value, 't2': str_value}}
        """
        if not self.conn:
            raise ConnectionError("数据库未连接。")

        # 如果没有提供共享字典，创建新的字典
        if shared_dict is None:
            data_dict = {}
        else:
            data_dict = shared_dict

        try:
            # 执行查询
            query_start = time.perf_counter()
            stmt = ibm_db.exec_immediate(self.conn, self.query)

            query_exec_time = time.perf_counter() - query_start
            logger.info(f"查询执行完成: {query_exec_time:.3f}s - {self.db_config['ip']}")

            if not stmt:
                raise Exception("查询执行失败")

            # 获取列信息
            num_fields = ibm_db.num_fields(stmt)
            if num_fields == 0:
                return data_dict
            columns = [ibm_db.field_name(stmt, i) for i in range(num_fields)]

            # 逐行获取数据并处理
            total_rows = 0
            fetch_start = time.perf_counter()

            while True:
                # 获取一行数据作为关联数组
                row = ibm_db.fetch_assoc(stmt)
                if not row:
                    break

                total_rows += 1

                # 提取KEY（假设第一列是KEY）
                key_column = columns[0]
                key = str(row[key_column]) if row[key_column] is not None else ""

                # 直接使用str(result)
                row_str = str(row)

                if data_source == 't1':
                    # 第一个数据源：直接存储
                    data_dict[key] = {'t1': row_str, 't2': ''}
                else:
                    # 第二个数据源：获取或创建item
                    if key in data_dict:
                        # key存在：来自第一个数据源的记录
                        item = data_dict[key]
                        if item['t1'] == row_str:
                            # 数据相同，从字典中删除（节省内存）
                            del data_dict[key]
                    else:
                        # key不存在：第二个数据源独有的记录
                        item = {'t1': '', 't2': row_str}
                        data_dict[key] = item

            # 释放资源
            ibm_db.free_result(stmt)
            fetch_time = time.perf_counter() - fetch_start

            if data_source == 't1':
                logger.info(f"数据源A: {total_rows:,}, 查询: {query_exec_time:.3f}s, 存储: {fetch_time:.3f}s")
            else:
                logger.info(f"数据源B: {total_rows:,}, 查询: {query_exec_time:.3f}s, 存储: {fetch_time:.3f}s")

            return data_dict

        except Exception as e:
            logger.error(f"一次性数据获取失败: {e}")
            raise

    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        return {
            'total_fetched': self._stats['total_fetched'],
            'fetch_time': self._stats['fetch_time'],
            'connection_time': self._stats['connection_time'],
            'avg_fetch_rate': (
                self._stats['total_fetched'] / self._stats['fetch_time']
                if self._stats['fetch_time'] > 0 else 0
            ),
            'batch_size': self.batch_size,
            'use_connection_pool': self._use_connection_pool,
            'cached_queries': len(self.query_cache)
        }

    def reset_stats(self):
        """重置性能统计"""
        self._stats = {
            'total_fetched': 0,
            'fetch_time': 0.0,
            'connection_time': 0.0
        }

    def optimize_batch_size(self, target_memory_mb: int = 100):
        """根据目标内存使用量优化批次大小"""
        # 估算每行数据大小（粗略估计）
        estimated_row_size = 1024  # 1KB per row
        target_memory_bytes = target_memory_mb * 1024 * 1024
        optimal_batch_size = max(1000, min(target_memory_bytes // estimated_row_size, 50000))

        old_batch_size = self.batch_size
        self.batch_size = optimal_batch_size

        logger.info(f"批次大小优化: {old_batch_size} -> {optimal_batch_size} (目标内存: {target_memory_mb}MB)")

    def __getstate__(self):
        """支持pickle序列化：排除不可序列化的属性"""
        state = self.__dict__.copy()
        # 移除不可序列化的属性
        state['conn'] = None
        state['_iterator'] = None
        state['_connection_pool'] = None
        return state

    def __setstate__(self, state):
        """支持pickle反序列化：恢复对象状态"""
        self.__dict__.update(state)
        # 重新初始化不可序列化的属性
        self.conn = None
        self._iterator = None
        self._connection_pool = get_connection_pool()

        # 确保驱动配置
        self._ensure_driver_configured()

    def __del__(self):
        """析构函数：确保资源清理"""
        try:
            self.close()
        except:
            pass  # 忽略析构时的异常


