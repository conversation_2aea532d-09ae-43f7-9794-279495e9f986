# reporters/reporter_factory.py
import logging
from typing import List, Dict, Any, Optional
from .base_reporter import BaseReporter
from .csvfile_reporter import CsvFileReporter
from .csvpair_reporter import CsvPairReporter
from .sqlite_reporter import SqliteReporter
from .postgres_reporter import PostgresReporter

logger = logging.getLogger(__name__)

class ReporterFactory:
    """报告器工厂，用于根据配置创建报告器实例"""

    @staticmethod
    def create_reporter(reporter_config: Dict[str, Any], task_id: str) -> Optional[BaseReporter]:
        """根据配置创建报告器实例"""
        reporter_type = reporter_config.get('type')
        try:
            if reporter_type == 'sqlite':
                config = {
                    'db_path': reporter_config.get('db_path', f'task_{task_id}_results.db'),
                    'task_id': task_id,
                    'batch_size': reporter_config.get('batch_size', 10000)
                }
                return SqliteReporter(config)

            elif reporter_type == 'postgresql':
                required_keys = ['host', 'port', 'database', 'username', 'password']
                if not all(key in reporter_config for key in required_keys):
                    raise ValueError("PostgreSQL报告器缺少必要的配置参数")
                
                config = {
                    'host': reporter_config['host'],
                    'port': reporter_config['port'],
                    'database': reporter_config['database'],
                    'username': reporter_config['username'],
                    'password': reporter_config['password'],
                    'task_id': task_id,
                    'batch_size': reporter_config.get('batch_size', 10000)
                }
                return PostgresReporter(config)

            elif reporter_type in ['csv_pair', 'file_pair']:
                config = {
                    'data_dir': reporter_config.get('data_dir', './data')
                }
                return CsvPairReporter(config)

            else:
                config = {
                    'data_dir': reporter_config.get('data_dir', './data')
                }
                return CsvFileReporter(config)

        except Exception as e:
            logger.error(f"创建报告器 '{reporter_type}' 失败: {e}", exc_info=True)
            # 实现降级逻辑，如果PostgreSQL失败，则尝试创建SQLite
            if reporter_config.get('fallback_to_sqlite', False):
                fallback_config = {'type': 'sqlite'}
                return ReporterFactory.create_reporter(fallback_config, task_id)
            return None

    @staticmethod
    def create_reporters(reporter_config: Dict[str, Any], task_id: str) -> List[BaseReporter]:
        """根据配置创建多个报告器实例"""
        reporters = []

        # 检查是否是新的配置格式
        if 'active' in reporter_config and 'configurations' in reporter_config:
            active_reporters = reporter_config.get('active', [])
            configurations = reporter_config.get('configurations', {})

            for reporter_name in active_reporters:
                if reporter_name in configurations:
                    config = configurations[reporter_name]
                    reporter = ReporterFactory.create_reporter(config, task_id)
                    return reporter
                else:
                    logger.warning(f"[configurations]中找不到报告器配置: {reporter_name}")

        # 兼容旧的配置格式
        elif not reporter_config or reporter_config.get('type') == 'sqlite':
            sqlite_config = reporter_config if reporter_config else {}
            reporter = ReporterFactory.create_reporter({'type': 'sqlite', **sqlite_config}, task_id)
            return reporter

        elif reporter_config.get('type') == 'multiple':
            for config in reporter_config.get('reporters', []):
                reporter = ReporterFactory.create_reporter(config, task_id)
                return reporter

        else:
            reporter = ReporterFactory.create_reporter(reporter_config, task_id)
            return reporter

        return None