# core/engine.py
import time
import logging
from typing import Optional
from core.models import Record
from models.sqlalchemy_models import DiffStatus
from connectors.base_connector import BaseConnector
from reporters.base_reporter import BaseReporter

# Configure logging
logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

def compare_values(value_a, value_b) -> bool:
    """极致优化的值比较函数"""
    # 最快的比较：身份比较
    if value_a is value_b:
        return True

    # 快速None检查
    if value_a is None or value_b is None:
        return False  # 如果一个是None另一个不是，直接返回False

    # 类型检查优化
    type_a, type_b = type(value_a), type(value_b)
    if type_a is type_b:
        # 相同类型的快速比较
        if type_a in (str, int, float, bool):
            # 基础类型直接比较，最常见的情况
            return value_a == value_b
        elif type_a is dict:
            # 字典比较优化
            if len(value_a) != len(value_b):
                return False
            # 使用viewkeys()比较键（Python 2/3兼容）
            if value_a.keys() != value_b.keys():
                return False
            # 批量比较值
            for k in value_a:
                if not compare_values(value_a[k], value_b[k]):
                    return False
            return True
        else:
            # 其他类型
            return value_a == value_b

    # 类型不同时的快速字符串比较
    try:
        # 避免重复的str()调用
        if type_a is str:
            str_a = value_a
        else:
            str_a = str(value_a)

        if type_b is str:
            str_b = value_b
        else:
            str_b = str(value_b)

        return str_a == str_b
    except:
        return False

def compare_sources(source_a: BaseConnector, source_b: BaseConnector, reporter: Optional[BaseReporter]) -> dict:
    """
    核心比对引擎，流式归并算法
    :param source_a: 数据源 A 的连接器实例。
    :param source_b: 数据源 B 的连接器实例。
    :param reporter: 差异报告器实例。
    """

    source_only = 0 # 源始数据库独有的记录数
    target_only = 0 # 目标数据库独有的记录数
    diff_record = 0 # AB中字段值差异的记录数
    total_records = 0 # 总差异记录数
    treated_count = 0 # 已处理的记录数

    try:
        start_time = time.perf_counter()

        # 预先检查reporter状态
        has_reporter = reporter is not None
        has_report_match = has_reporter and hasattr(reporter, 'report_match')
        has_sqlalchemysv = has_reporter and hasattr(reporter, '_sqlalchemy_service')

        with source_a, source_b, reporter:
            iter_a = iter(source_a)
            iter_b = iter(source_b)

            record_a: Optional[Record] = next(iter_a, None)
            record_b: Optional[Record] = next(iter_b, None)

            lastlog_time = start_time
            while record_a is not None or record_b is not None:
                treated_count += 1
                if treated_count % 100000 == 0:
                    current_time = time.perf_counter()
                    duration = current_time - lastlog_time
                    rate = 100000 / duration if duration > 0 else float('inf')
                    logger.info(f"已处理 {treated_count} 条记录，速率: {rate:.0f} 条/秒，差异: {source_only + target_only + diff_record}")

                    # 调用reporter的进度回调
                    if has_report_match:
                        reporter.report_match(f"progress_{treated_count}", treated_count, total_records)

                    # 如果reporter有SQLAlchemy服务，更新任务进度
                    if has_sqlalchemysv:
                        try:
                            # 比对阶段进度计算：5%-95%的进度空间，共90%的进度范围
                            # 使用对数增长模型，避免进度停滞，确保持续增长但不会过快到达95%
                            import math
                            if treated_count <= 100000:
                                # 前10万条记录：5% - 30%
                                progress_pct = 5.0 + (treated_count / 100000) * 25.0
                            elif treated_count <= 1000000:
                                # 10万-100万条记录：30% - 60%
                                progress_pct = 30.0 + ((treated_count - 100000) / 900000) * 30.0
                            else:
                                # 100万条以上：60% - 95%（使用对数增长，避免到达95%）
                                log_factor = math.log10(treated_count / 1000000 + 1)
                                progress_pct = min(95.0, 60.0 + log_factor * 35.0)

                            # 更新任务进度
                            reporter._sqlalchemy_service.update_task_progress(
                                reporter._task_id,
                                progress_pct,
                                f"流式比对进行中 - 已处理 {treated_count:,} 条记录，差异 {source_only + target_only + diff_record:,} 条",
                                processed_records=treated_count
                            )
                        except Exception as e:
                            # 使用debug级别日志，避免影响主要日志输出
                            logger.debug(f"更新任务进度失败: {e}")

                    lastlog_time = current_time

                if record_a and (record_b is None or record_a.key < record_b.key):
                    # 处理A中独有的记录
                    source_only += 1
                    if has_reporter:
                        diff_result = {
                            'record_key': record_a.key,
                            'status': DiffStatus.SOURCE_ONLY.value,
                            'source_value': record_a.value,
                            'target_value': None
                        }
                        reporter.report_diff(diff_result)
                    record_a = next(iter_a, None)

                elif record_b and (record_a is None or record_b.key < record_a.key):
                    # 处理B中独有的记录
                    target_only += 1
                    if has_reporter:
                        diff_result = {
                            'record_key': record_b.key,
                            'status': DiffStatus.TARGET_ONLY.value,
                            'source_value': None,
                            'target_value': record_b.value
                        }
                        reporter.report_diff(diff_result)
                    record_b = next(iter_b, None)

                elif record_a and record_b:
                    total_records += 2
                    # 处理键相同的记录
                    if not compare_values(record_a.value, record_b.value):
                        diff_record += 1
                        if has_reporter:
                            diff_result = {
                                'record_key': record_a.key,
                                'status': DiffStatus.DIFFERENT.value,
                                'source_value': record_a.value,
                                'target_value': record_b.value
                            }
                            reporter.report_diff(diff_result)
                    record_a = next(iter_a, None)
                    record_b = next(iter_b, None)

            total_records += source_only + target_only

    except Exception as e:
        logger.error(f"比对过程中发生错误: {e}")
        raise
    finally:
        # 自动刷新报告器的剩余缓存数据
        if has_reporter and hasattr(reporter, 'force_flush'):
            try:
                reporter.force_flush()
            except Exception as e:
                logger.error(f"刷新报告器缓存失败: {e}")

        # 计算执行时间和统计信息
        exec_time = time.perf_counter() - start_time
        processing_rate = treated_count / exec_time if exec_time > 0 else 0

        logger.info(f"数据比对完成耗时: {exec_time:.2f}秒，处理记录: {treated_count:,}条，速率: {processing_rate:.0f}条/秒")
        logger.info(f"A源: {source_only}条，B源: {target_only}条，差异: {source_only + target_only + diff_record}条")

        return {
            'total_records': total_records,
            'processed_records': treated_count,
            'diff_records': diff_record,
            'source_only': source_only,
            'target_only': target_only,
            'exec_time': exec_time,
            'processing_rate': processing_rate
        }

def compare_sources_memory(source_a: BaseConnector, source_b: BaseConnector, reporter: Optional[BaseReporter],) -> dict:
    """内存字典算法比对两个数据源"""

    source_only = 0  # 源端独有的记录数
    target_only = 0  # 目标端独有的记录数
    diff_record = 0  # AB中字段值差异的记录数
    total_records = 0  # 总差异记录数
    treated_count = 0  # 已处理的记录数

    load_a_time = 0.0
    load_b_time = 0.0
    report_time = 0.0

    try:
        start_time = time.perf_counter()

        with source_a, source_b:
            # 阶段1：加载数据源A到共享字典
            logger.info("加载数据源A...")
            load_start = time.perf_counter()

            # 创建共享字典
            data_result = source_a.fetch_all_data(shared_dict=None, data_source='t1')
            source_a_count = len(data_result)

            load_a_time = time.perf_counter() - load_start
            logger.info(f"数据源A加载完成: {source_a_count:,} 条记录，耗时: {load_a_time:.2f}秒")

            # 调用进度回调 - 数据源A加载完成（仅当reporter存在时）
            if reporter and hasattr(reporter, 'report_match'):
                reporter.report_match("load_a_complete", source_a_count, 0)

            # 更新SQLAlchemy任务进度 - 数据源A加载完成
            if reporter and hasattr(reporter, '_sqlalchemy_service') and hasattr(reporter, '_task_id'):
                try:
                    reporter._sqlalchemy_service.update_task_progress(
                        reporter._task_id,
                        25.0,  # 数据源A加载完成，进度25%（在5%-95%范围内）
                        f"数据源A加载完成 - {source_a_count:,} 条记录"
                    )
                except Exception as e:
                    logger.debug(f"更新进度失败: {e}")

            # 阶段2：加载数据源B到同一个字典
            logger.info("加载数据源B...")
            load_start = time.perf_counter()

            # 使用共享字典加载数据源B
            data_result = source_b.fetch_all_data(shared_dict=data_result, data_source='t2')
            source_b_count = sum(1 for item in data_result.values() if item['t2'] != '')

            load_b_time = time.perf_counter() - load_start
            logger.info(f"数据源B加载完成: {source_b_count:,} 条记录，耗时: {load_b_time:.2f}秒")

            # 调用进度回调 - 数据源B加载完成（仅当reporter存在时）
            if reporter and hasattr(reporter, 'report_match'):
                reporter.report_match("load_b_complete", source_a_count + source_b_count, 0)

            # 更新SQLAlchemy任务进度 - 数据源B加载完成
            if reporter and hasattr(reporter, '_sqlalchemy_service') and hasattr(reporter, '_task_id'):
                try:
                    reporter._sqlalchemy_service.update_task_progress(
                        reporter._task_id,
                        50.0,  # 数据源B加载完成，进度50%（在5%-95%范围内）
                        f"数据源B加载完成 - {source_b_count:,} 条记录"
                    )
                except Exception as e:
                    logger.debug(f"更新进度失败: {e}")

            # 阶段3：报告差异（比对已在数据加载时完成）
            logger.info("统计和报告差异结果...")
            report_start = time.perf_counter()

            # 重置统计计数器，准确统计各种差异类型
            source_only = 0
            target_only = 0
            diff_record = 0
            processed = 0

            # 遍历所有差异记录进行报告和统计
            for key, item in data_result.items():
                processed += 1

                # 确定差异类型并进行统计
                if item["t1"] == "":
                    # 目标端独有记录
                    target_only += 1
                    if reporter:
                        diff_result = {
                            'record_key': str(key),
                            'status': DiffStatus.TARGET_ONLY.value,
                            'source_value': None,
                            'target_value': item["t2"]
                        }
                        reporter.report_diff(diff_result)
                elif item["t2"] == "":
                    # 源端独有记录
                    source_only += 1
                    if reporter:
                        diff_result = {
                            'record_key': str(key),
                            'status': DiffStatus.SOURCE_ONLY.value,
                            'source_value': item["t1"],
                            'target_value': None
                        }
                        reporter.report_diff(diff_result)
                else:
                    # 字段值差异记录
                    diff_record += 1
                    if reporter:
                        diff_result = {
                            'record_key': str(key),
                            'status': DiffStatus.DIFFERENT.value,
                            'source_value': item["t1"],
                            'target_value': item["t2"]
                        }
                        reporter.report_diff(diff_result)

                # 定期输出进度
                if processed % 100000 == 0:
                    logger.info(f"已处理 {processed:,}/{len(data_result):,} 条差异记录")

                    # 更新SQLAlchemy任务进度 - 差异报告进度
                    if reporter and hasattr(reporter, '_sqlalchemy_service') and hasattr(reporter, '_task_id'):
                        try:
                            # 差异报告阶段进度：50%-95%（在5%-95%范围内）
                            report_progress = 50.0 + (processed / len(data_result)) * 45.0
                            reporter._sqlalchemy_service.update_task_progress(
                                reporter._task_id,
                                report_progress,
                                f"生成差异报告 - {processed:,}/{len(data_result):,} 条记录"
                            )
                        except Exception as e:
                            logger.debug(f"更新进度失败: {e}")

            # 计算总差异记录数和已处理记录数
            total_records = diff_record + source_only + target_only
            treated_count = source_a_count + source_b_count

            report_time = time.perf_counter() - report_start
            logger.info(f"差异报告完成，耗时: {report_time:.2f}秒")

    except Exception as e:
        logger.error(f"比对过程中发生错误: {e}", exc_info=True)
        raise
    finally:
        # 自动刷新报告器的剩余缓存数据（仅当reporter存在时）
        if reporter and hasattr(reporter, 'force_flush'):
            try:
                reporter.force_flush()
            except Exception as e:
                logger.error(f"刷新报告器缓存失败: {e}")

        # 输出最终统计
        end_time = time.perf_counter()
        total_time = end_time - start_time
        overall_rate = treated_count / total_time if total_time > 0 else 0

        logger.info(f"数据比对完成耗时: {total_time:.2f}秒，处理记录: {treated_count:,}条，速率: {overall_rate:.0f}条/秒")
        logger.info(f"A源: {source_only:,}条，B源: {target_only:,}条，差异: {total_records:,}条")
        logger.info(f"时间分布 - 加载A: {load_a_time:.2f}s, 加载B: {load_b_time:.2f}s, 报告: {report_time:.2f}s")

        # 返回准确的统计信息
        return {
            'total_records': int(total_records),
            'processed_records': int(treated_count),
            'diff_records': int(diff_record),
            'source_only': int(source_only),
            'target_only': int(target_only),
            'exec_time': float(total_time),
            'processing_rate': float(overall_rate)
        }

