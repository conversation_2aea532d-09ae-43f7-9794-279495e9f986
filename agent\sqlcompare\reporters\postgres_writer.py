#!/usr/bin/env python3
"""
原生PostgreSQL高性能写入器
绕过SQLAlchemy ORM，直接使用psycopg2实现高性能批量写入
"""
import io
import csv
import time
import threading
from typing import Dict, Any, List, Optional, Tuple
from contextlib import contextmanager
import logging

try:
    import psycopg2
    import psycopg2.extras
    import psycopg2.pool
except ImportError:
    psycopg2 = None

logger = logging.getLogger(__name__)


class NativePostgreSQLConnectionPool:
    """原生PostgreSQL连接池管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        if not psycopg2:
            raise ImportError("psycopg2 is required for native PostgreSQL operations")
        
        self.config = config
        self.pool = None
        self._lock = threading.Lock()
        self._initialize_pool()
    
    def _initialize_pool(self):
        """初始化连接池"""
        try:
            # 构建连接参数
            connection_params = {
                'host': self.config.get('host', 'localhost'),
                'port': self.config.get('port', 5432),
                'database': self.config.get('database', 'postgres'),
                'user': self.config.get('username', 'postgres'),
                'password': self.config.get('password', ''),
                'application_name': 'SQLCompare_Native'
            }
            
            # 连接池配置
            minconn = self.config.get('pool_min_connections', 1)
            maxconn = self.config.get('pool_max_connections', 10)
            
            # 创建线程安全的连接池
            self.pool = psycopg2.pool.ThreadedConnectionPool(
                minconn=minconn,
                maxconn=maxconn,
                **connection_params
            )
            
            logger.info(f"原生PostgreSQL连接池初始化成功: {minconn}-{maxconn} 连接")
            
        except Exception as e:
            logger.error(f"原生PostgreSQL连接池初始化失败: {e}")
            raise
    
    @contextmanager
    def get_connection(self):
        """获取连接（上下文管理器）"""
        connection = None
        try:
            with self._lock:
                connection = self.pool.getconn()
            yield connection
        finally:
            if connection:
                with self._lock:
                    self.pool.putconn(connection)
    
    def close_all(self):
        """关闭所有连接"""
        if self.pool:
            self.pool.closeall()
            logger.info("原生PostgreSQL连接池已关闭")


class NativePostgreSQLWriter:
    """原生PostgreSQL高性能写入器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.connection_pool = NativePostgreSQLConnectionPool(config)
        
        # 性能配置
        self.batch_size = config.get('batch_size', 50000)
        self.use_copy = config.get('use_copy', True)
        self.commit_interval = config.get('commit_interval', self.batch_size)
        
        # 统计信息
        self.stats = {
            'total_inserts': 0,
            'total_batches': 0,
            'total_time': 0.0,
            'avg_throughput': 0.0
        }
        
        logger.info(f"原生PostgreSQL写入器初始化完成")
        logger.info(f"配置 - batch_size: {self.batch_size}, use_copy: {self.use_copy}")
    
    def ensure_table_exists(self, table_name: str = 'comparison_results'):
        """确保表存在（优化的表结构）"""
        
        create_table_sql = f"""
        CREATE TABLE IF NOT EXISTS {table_name} (
            id BIGSERIAL PRIMARY KEY,
            task_id VARCHAR(255) NOT NULL,
            table_name VARCHAR(255) NOT NULL,
            record_key TEXT NOT NULL,
            status VARCHAR(50) NOT NULL,
            field_name VARCHAR(255),
            source_value TEXT,
            target_value TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        -- 创建高性能索引（如果不存在）
        CREATE INDEX IF NOT EXISTS idx_{table_name}_task_id ON {table_name}(task_id);
        CREATE INDEX IF NOT EXISTS idx_{table_name}_table_name ON {table_name}(table_name);
        CREATE INDEX IF NOT EXISTS idx_{table_name}_status ON {table_name}(status);
        CREATE INDEX IF NOT EXISTS idx_{table_name}_task_table ON {table_name}(task_id, table_name);
        CREATE INDEX IF NOT EXISTS idx_{table_name}_task_status ON {table_name}(task_id, status);
        """
        
        try:
            with self.connection_pool.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(create_table_sql)
                    conn.commit()
            logger.info(f"表 {table_name} 和索引确保完成")
        except Exception as e:
            logger.error(f"创建表失败: {e}")
            raise
    
    def batch_insert_copy(self, data_list: List[Dict[str, Any]], table_name: str = 'comparison_results') -> Tuple[int, float]:
        """使用COPY FROM STDIN进行批量插入（最高性能）"""
        
        if not data_list:
            return 0, 0.0
        
        start_time = time.perf_counter()
        
        try:
            with self.connection_pool.get_connection() as conn:
                with conn.cursor() as cursor:
                    # 准备COPY数据
                    copy_data = io.StringIO()
                    writer = csv.writer(copy_data, delimiter='\t', lineterminator='\n')
                    
                    for record in data_list:
                        row_data = [
                            record.get('task_id', ''),
                            record.get('table_name', ''),
                            record.get('record_key', ''),
                            record.get('status', ''),
                            record.get('field_name'),
                            record.get('source_value'),
                            record.get('target_value')
                        ]
                        
                        # 处理None值为PostgreSQL的NULL
                        processed_row = ['\\N' if v is None else str(v) for v in row_data]
                        writer.writerow(processed_row)
                    
                    copy_data.seek(0)
                    
                    # 执行COPY命令
                    cursor.copy_from(
                        copy_data,
                        table_name,
                        columns=('task_id', 'table_name', 'record_key', 'status', 'field_name', 'source_value', 'target_value'),
                        sep='\t',
                        null='\\N'
                    )
                    
                    conn.commit()
                    
            inserted_count = len(data_list)
            elapsed_time = time.perf_counter() - start_time
            
            # 更新统计信息
            self.stats['total_inserts'] += inserted_count
            self.stats['total_batches'] += 1
            self.stats['total_time'] += elapsed_time
            
            throughput = inserted_count / elapsed_time if elapsed_time > 0 else 0
            logger.debug(f"COPY插入完成: {inserted_count} 条记录, 耗时: {elapsed_time:.3f}秒, 吞吐量: {throughput:.0f} 记录/秒")
            
            return inserted_count, elapsed_time
            
        except Exception as e:
            logger.error(f"COPY批量插入失败: {e}")
            raise
    
    def batch_insert_execute_values(self, data_list: List[Dict[str, Any]], table_name: str = 'comparison_results') -> Tuple[int, float]:
        """使用execute_values进行批量插入（高性能）"""
        
        if not data_list:
            return 0, 0.0
        
        start_time = time.perf_counter()
        
        try:
            with self.connection_pool.get_connection() as conn:
                with conn.cursor() as cursor:
                    # 准备数据
                    values_data = []
                    for record in data_list:
                        values_data.append((
                            record.get('task_id', ''),
                            record.get('table_name', ''),
                            record.get('record_key', ''),
                            record.get('status', ''),
                            record.get('field_name'),
                            record.get('source_value'),
                            record.get('target_value')
                        ))
                    
                    # 执行批量插入
                    insert_sql = f"""
                    INSERT INTO {table_name} 
                    (task_id, table_name, record_key, status, field_name, source_value, target_value)
                    VALUES %s
                    """
                    
                    psycopg2.extras.execute_values(
                        cursor,
                        insert_sql,
                        values_data,
                        template=None,
                        page_size=self.batch_size
                    )
                    
                    conn.commit()
                    
            inserted_count = len(data_list)
            elapsed_time = time.perf_counter() - start_time
            
            # 更新统计信息
            self.stats['total_inserts'] += inserted_count
            self.stats['total_batches'] += 1
            self.stats['total_time'] += elapsed_time
            
            throughput = inserted_count / elapsed_time if elapsed_time > 0 else 0
            logger.debug(f"execute_values插入完成: {inserted_count} 条记录, 耗时: {elapsed_time:.3f}秒, 吞吐量: {throughput:.0f} 记录/秒")
            
            return inserted_count, elapsed_time
            
        except Exception as e:
            logger.error(f"execute_values批量插入失败: {e}")
            raise
    
    def batch_insert(self, data_list: List[Dict[str, Any]], table_name: str = 'comparison_results') -> Dict[str, Any]:
        """智能批量插入（自动选择最优方法）"""
        
        if not data_list:
            return {'inserted_count': 0, 'insert_time': 0.0, 'method': 'none', 'throughput': 0.0}
        
        try:
            # 优先使用COPY方法
            if self.use_copy:
                inserted_count, insert_time = self.batch_insert_copy(data_list, table_name)
                method = 'native_copy'
            else:
                inserted_count, insert_time = self.batch_insert_execute_values(data_list, table_name)
                method = 'native_execute_values'
            
            throughput = inserted_count / insert_time if insert_time > 0 else 0
            
            return {
                'inserted_count': inserted_count,
                'insert_time': insert_time,
                'method': method,
                'throughput': throughput
            }
            
        except Exception as e:
            logger.error(f"批量插入失败: {e}")
            raise
    
    def get_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        if self.stats['total_time'] > 0:
            self.stats['avg_throughput'] = self.stats['total_inserts'] / self.stats['total_time']
        
        return self.stats.copy()
    
    def clear_task_data(self, task_id: str, table_name: str = 'comparison_results'):
        """清理指定任务的数据"""
        try:
            with self.connection_pool.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(f"DELETE FROM {table_name} WHERE task_id = %s", (task_id,))
                    deleted_count = cursor.rowcount
                    conn.commit()
            
            logger.info(f"清理任务 {task_id} 的数据: {deleted_count} 条记录")
            return deleted_count
            
        except Exception as e:
            logger.error(f"清理任务数据失败: {e}")
            raise
    
    def close(self):
        """关闭连接池"""
        if self.connection_pool:
            self.connection_pool.close_all()
            logger.info("原生PostgreSQL写入器已关闭")
