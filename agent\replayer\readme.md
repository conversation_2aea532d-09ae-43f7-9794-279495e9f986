# ArkReplay-Agent 高性能流量回放执行引擎

## 项目概述

ArkReplay-Agent是ArkReplay系统的核心组件，采用C++17实现的高性能流量回放执行引擎。专为金融交易系统设计，支持多种交易协议（KCXP、FIX、MID），提供精确的按序回放、实时响应收集和高效的数据处理能力。

## 核心特性

### 🚀 高性能设计
- **内存池管理**：减少动态内存分配开销，提高内存使用效率
- **零拷贝架构**：移动语义和智能指针，避免不必要的数据拷贝
- **缓存友好**：64字节对齐的数据结构，优化CPU缓存性能
- **无锁编程**：原子操作和线程本地存储，减少锁竞争

### 🔧 模块化架构
- **核心数据结构**：高效的消息、会话和统计信息管理
- **协议解析**：可扩展的协议解析框架，支持FIX、KCXP、MID
- **网络通信**：异步网络I/O，支持TCP、UDP、组播
- **回放引擎**：智能任务调度和精确时序控制
- **配置管理**：灵活的配置系统，支持多种格式
- **日志系统**：高性能异步日志，支持多种输出格式

### 📊 实时监控
- **性能指标**：吞吐量、延迟、成功率等关键指标
- **资源监控**：内存使用、网络流量、CPU利用率
- **错误追踪**：详细的错误信息和调用栈
- **统计分析**：实时统计和历史趋势分析

## 数据结构设计

ArkReplay-Agent采用现代C++设计模式，基于以下核心原则：

### 设计原则
- **高性能优先**：内存池、零拷贝、缓存友好的数据结构设计
- **模块化架构**：清晰的关注点分离，便于维护和扩展
- **线程安全**：支持高并发处理，避免数据竞争
- **内存高效**：优化内存使用，减少碎片化
- **可扩展性**：支持新协议和功能的快速集成

### 1.1 系统核心价值

ArkReplay系统通过录制、回放和比对金融交易系统的请求和响应，实现以下核心价值：

- **问题复现**：精确重现生产环境中出现的问题，加速问题定位和解决
- **回归测试**：将历史交易数据转化为自动化测试用例，确保系统变更不影响现有功能
- **性能评估**：通过回放真实交易负载，评估系统性能和容量
- **数据一致性验证**：比对回放前后的数据库状态，确保交易处理的正确性
- **测试数据管理**：提供测试数据的版本控制、分类和重用机制

系统支持多种金融交易协议（MID、FIX、KCXP等），并提供跨异构数据库的比对能力，适用于各类金融交易系统的测试和验证。

## 2. 系统架构

### 2.1 四层组件架构

ArkReplay采用四层组件架构，每层负责不同的功能：

1. **ArkReplay-Web (Vue.js/TypeScript)**：用户交互界面
   - 提供直观的操作体验和可视化展示
   - 实现测试计划管理、数据源配置、回放控制等前端功能
   - 通过组件化设计提供灵活的界面布局
   - 负责数据可视化和用户交互体验

2. **ArkReplay-Server (Python/Django)**：服务管理与配置中心
   - 提供RESTful API接口、任务调度、权限管理
   - 管理测试计划、存储分析结果
   - 协调Agent回放和DB-Agent比对
   - 处理业务逻辑和数据持久化

3. **ArkReplay-Agent (C++)**：高性能流量回放执行引擎
   - 负责协议解析、按序回放交易流量、收集原始响应
   - 专注于回放性能和准确性
   - 实现多种交易协议解析（KCXP、FIX、MID）
   - 提供高性能的数据处理能力

4. **ArkReplay DB-Agent (Python/FastAPI)**：数据库操作服务
   - 负责执行实际的数据库连接测试和数据比对
   - 封装数据库驱动（包括DB2、Oracle等复杂依赖）
   - 提供跨异构数据源的比对能力
   - 实现异步任务处理和状态管理

### 2.2 部署拓扑

```
┌────────────────┐   ┌────────────────┐   ┌────────────────┐   ┌────────────────┐   ┌────────────────┐
│ ArkReplay-Agent│   │ArkReplay-Server│   │ ArkReplay      │   │   存储服务器    │   │ArkReplay-Web   │
│ (C++ Replay)   │   │ (Python Mgmt)  │   │ DB-Agent       │   │                │   │  (Vue.js/TS)   │
│                │   │                │   │ (Python DB Ops)│   │                │   │                │
│- 协议解析       │   │- Web控制台     │   │- DB连接测试     │   │- PostgreSQL    │   │- 用户界面       │
│- 回放执行       │   │- API接口       │   │- 数据比对逻辑   │   │- Redis缓存      │   │- 数据可视化     │
│- 结果收集       │   │- 任务调度      │   │- 数据库驱动封装  │   │- 归档存储       │   │- 操作控制      │
│- 高性能核心     │   │- 测试计划管理   │   │- 结果收集       │   │- 高性能核心     │   │- 响应式布局     │
└────────────────┘   └────────────────┘   └────────────────┘   └────────────────┘   └────────────────┘
```

### 2.3 目录结构

```
ArkReplay/
├── web/                          # 前端实现 (Vue.js + TypeScript)
├── server/                       # 后端管理服务 (Django/DRF)
│   └── tests/                    # Server测试目录
├── agent/                        # 代理服务
│   ├── db/                       # 数据库代理服务 (FastAPI)
│   │   └── src/
│   │       └── tests/            # DB-Agent测试目录
│   └── player/                   # 流量回放模块 (C++)
├── docs/                         # 项目文档
├── scripts/                      # 脚本工具目录
├── config/                       # 全局配置目录
├── tests/                        # 项目级测试目录
│   ├── readme.md                 # 测试说明文档
│   └── run_all_tests.py          # 项目级测试运行器
└── start.bat                     # 启动脚本
```

### 2.4 前后端交互

1. **API通信**：
   - RESTful API：前端通过Axios调用后端API
   - 统一的请求/响应格式：确保数据交换一致性
   - 错误处理机制：统一处理API错误和异常

2. **实时通信**：
   - WebSocket：用于回放状态实时更新
   - 服务器推送：任务状态变更通知
   - 心跳机制：保持连接活跃

3. **权限控制**：
   - 基于JWT的认证：安全的用户认证
   - 基于角色的权限控制：不同角色访问不同功能
   - 前端权限路由：根据用户权限动态生成路由

### 2.5 前端模块与后端服务的映射关系

|-前端模块---|---对应后端服务--|--------主要功能--------|
测试计划管理 Server	          创建、编辑、执行测试计划
数据源管理	 Server	          配置和管理数据源
回放控制	    Server + Agent	 控制回放执行、监控回放状态
数据比对	    Server + DB-Agent 配置比对任务、查看比对结果
问题管理	    Server	          创建、跟踪、解决问题
报告生成	    Server	          生成和导出测试报告
系统管理	    Server	          用户、权限、系统配置管理

## 3. 技术栈

### 3.1 前端技术栈

- **框架**：Vue.js 3.5.13 + TypeScript
- **路由管理**：Vue Router 4.5.0
- **状态管理**：Pinia 2.3.0（替代了Vuex）
- **UI组件库**：Element Plus 2.9.0
- **HTTP请求**：Axios 1.9.0
- **图表可视化**：ECharts 5.6.0
- **CSS预处理**：SCSS + TailwindCSS
- **构建工具**：Vite 6.0.3

### 3.2 后端技术栈

#### Server服务 (Django)
- **Web框架**：Django + Django REST Framework
- **异步处理**：Celery + Redis
- **数据库ORM**：Django ORM
- **WebSocket支持**：Django Channels

#### DB-Agent服务 (FastAPI)
- **Web框架**：FastAPI
- **异步支持**：asyncio + uvicorn
- **数据库驱动**：多种数据库连接器（包括psycopg2、pymysql、cx_Oracle、ibm_db等）
- **任务管理**：自定义异步任务系统

#### Agent服务 (C++)
- **网络库**：Boost.Asio
- **并发处理**：C++17 标准库
- **协议解析**：自定义解析器
- **性能优化**：内存池、无锁队列

### 3.3 数据存储

- **主数据库**：PostgreSQL
- **缓存系统**：Redis
- **文件存储**：本地文件系统

## 4. 核心功能模块

### 4.1 测试计划管理

- 测试计划是整个工作流程的核心，定义了回放的范围、目标和策略
- 支持计划创建、数据源关联、执行控制和结果跟踪
- 实现测试计划的版本管理和历史比较

### 4.2 数据源管理

- 支持多种数据源：日志解析、报文筛选、手工录入、问题转换
- 提供批次管理、数据有效性验证和数据源版本控制
- 实现数据源的创建、编辑和管理

### 4.3 反演回放

- 将测试计划中定义的请求发送到目标系统并收集响应
- 支持回放控制（速度、暂停、继续、终止）和执行监控
- 实现实时监控回放进度、性能指标和异常情况

### 4.4 应答比对

- 比对原始响应与回放响应，识别并分析差异
- 提供差异库存储和分类所有差异结果
- 支持差异确认、忽略、转问题等操作

### 4.5 数据比对

- 提供高效的数据库级比对能力，验证回放前后数据库数据的一致性
- 支持表结构比对、数据总量比对、表内容比对
- 实现跨异构数据源比对（Oracle、MySQL、PostgreSQL、SQLServer、DB2等）
- 提供多种比对算法：
  - 主键比对算法（默认）：基于主键的精确比对
  - 哈希比对算法：适用于大规模数据集
  - 两阶段比对算法：适用于差异较大的数据集

### 4.6 问题管理

- 从差异结果创建问题记录
- 按严重程度、优先级和类型分类问题
- 跟踪问题从发现到解决的全过程

### 4.7 测试报告

- 自动生成全面的测试报告
- 提供统计分析、问题汇总和趋势分析
- 支持多种格式导出（PDF、Excel、HTML）

## 5. 前端详解

### 5.1 前端架构

ArkReplay-Web采用现代化的前端架构，主要包括以下几个部分：

1. **核心框架层**：
   - Vue.js 3.5.13 + TypeScript：提供响应式数据绑定和类型安全
   - Vue Router 4.5.0：实现单页应用路由管理
   - Pinia 2.3.0：状态管理，替代了Vuex
   - Vite 6.0.3：高效的构建工具

2. **UI组件层**：
   - Element Plus 2.9.0：企业级UI组件库
   - 自定义业务组件：封装特定业务逻辑的组件
   - 布局系统：响应式布局适配不同设备

3. **数据交互层**：
   - Axios 1.9.0：HTTP请求库
   - WebSocket：实时数据更新和通知
   - 数据转换适配器：处理前后端数据格式转换

4. **可视化层**：
   - ECharts 5.6.0：数据图表可视化
   - 自定义可视化组件：特定业务场景的可视化
   - 差异比对可视化：展示数据比对结果

5. **业务模块层**：
   - 测试计划管理模块
   - 数据源配置模块
   - 回放控制模块
   - 比对分析模块
   - 报告生成模块
   - 系统管理模块

### 5.2 前端技术特点

1. **组件化设计**：
   - 将UI拆分为可复用的组件
   - 业务逻辑与UI表现分离
   - 提高代码复用率和可维护性

2. **响应式布局**：
   - 适配不同屏幕尺寸和设备
   - 提供一致的用户体验

3. **状态管理**：
   - 集中管理应用状态
   - 实现组件间的数据共享
   - 提供可预测的状态变更

4. **性能优化**：
   - 路由懒加载
   - 组件按需加载
   - 虚拟滚动处理大数据集
   - 缓存策略减少请求

5. **主题定制**：
   - 支持明暗主题切换
   - 可定制的UI主题
   - 符合企业VI的设计风格

## 6. 数据比对功能详解

### 6.1 比对算法

系统实现了多种数据比对算法，以适应不同场景：

1. **默认主键比对算法**：
   - 以主键作为匹配依据
   - 主键相同的记录视为匹配项进行比对
   - 主键不同的记录视为缺失
   - 适用于一般场景

2. **哈希比对算法**：
   - 使用哈希函数快速定位差异
   - 适用于数据量较大且数据分布均匀的情况
   - 通过哈希值比较提高比对效率

3. **两阶段比对算法**：
   - 第一阶段确定记录存在性
   - 第二阶段对共同存在的记录进行详细比对
   - 适用于差异较大的数据集

### 6.2 比对流程

1. 创建比对任务，指定源数据库和目标数据库
2. 选择比对算法和配置比对参数
3. 系统异步执行比对任务
4. 生成比对结果，包括匹配记录、差异记录、仅源存在记录、仅目标存在记录
5. 提供差异详情和统计信息
6. 支持导出比对结果为JSON、CSV或Excel格式

### 6.3 比对结果处理

- 结构化存储比对结果，包括摘要和详细差异
- 提供差异可视化展示和分析工具
- 支持将差异转化为问题记录
- 实现差异的确认、忽略和处理流程

### 6.4 错误处理机制

数据库比对系统实现了多层次的错误处理机制，确保错误能够被正确捕获、报告和展示：

1. **错误处理流程**：
   - 立即终止比对过程，避免执行不必要的操作
   - 记录详细的错误信息，包括错误类型、消息和上下文
   - 将任务状态更新为"失败"
   - 向Server报告错误信息
   - 在Web界面显示友好的错误提示

2. **错误报告结构**：
   ```json
   {
     "error": {
       "type": "DatabaseQueryError",
       "message": "无法获取源数据库的记录总数，请检查数据库连接和查询配置",
       "details": {
         "source_db_type": "db2",
         "source_db": "10.2.97.132:50000/SAMPLE",
         "target_db_type": "db2",
         "target_db": "10.2.97.162:50000/SAMPLE"
       }
     }
   }
   ```

3. **常见错误类型**：
   | 错误类型 | 描述 | 可能原因 |
   |---------|------|--------|
   | ConnectionError | 数据库连接失败 | 网络问题、凭证错误、数据库服务不可用 |
   | DatabaseQueryError | 数据库查询执行失败 | SQL语法错误、权限不足、表不存在 |
   | ConfigurationError | 配置错误 | 无效的比对配置参数 |
   | ValidationError | 数据验证失败 | 源表和目标表结构不兼容 |
   | TimeoutError | 操作超时 | 查询执行时间过长、网络延迟 |

## 7. 部署方式

### 7.1 开发环境部署

使用`start.bat`脚本启动各组件：
1. 启动Web前端：`cd web && pnpm dev`
2. 启动Server服务：`cd server && python manage.py runserver`
3. 启动DB-Agent服务：`cd agent/db/src/app && python main.py`

### 7.2 测试运行

项目提供了完整的测试套件，确保各组件功能正常：

#### 运行所有测试
```bash
# 运行项目完整测试套件
python tests/run_all_tests.py
```

#### 运行DB-Agent测试
```bash
# 进入DB-Agent测试目录
cd agent/db/src/tests

# 运行所有DB-Agent测试
python run_tests.py

# 运行单个测试
python test_startup.py
python test_simple_startup.py
```

#### 运行Server测试
```bash
# 进入Server测试目录
cd server/tests

# 运行所有Server测试
python run_tests.py

# 运行单个测试
python test_task_cleanup.py
python test_simple.py
```

#### 测试覆盖范围
- **DB-Agent测试**：启动测试、服务初始化、错误报告、通知功能、比较引擎、跨数据库比较、SQL解析器
- **Server测试**：任务清理功能、消息队列、WebSocket管理器、简单功能验证

### 7.3 容器化部署

- Web前端：基于`node:20-alpine`和`nginx:stable-alpine`的多阶段构建
- Server服务：基于Python的Docker镜像
- DB-Agent服务：基于Debian的多阶段构建，包含多种数据库客户端

### 7.4 部署方案

1. **单机部署**：适用于小型团队或测试环境
2. **分布式部署**：适用于大型企业生产环境
3. **容器化部署**：提供基于Docker的部署方案，支持Kubernetes编排

## 8. 系统特点

1. **模块化架构**：系统按功能划分为独立模块，减少耦合
2. **高性能设计**：针对大数据量优化性能，支持并行处理
3. **可扩展性**：设计灵活的接口和抽象层，便于扩展新功能
4. **多协议支持**：支持MID、FIX和KCXP等多种交易协议
5. **跨数据库比对**：支持多种数据库类型间的比对
6. **测试计划驱动**：以测试计划为中心驱动整个回放流程
7. **完整的错误处理**：包含错误处理和恢复机制，确保系统稳定

## 9. 未来规划

### 9.1 近期规划

- **测试计划体系增强**：
  - 测试计划模板库
  - 测试计划版本管理
  - 测试计划依赖关系
- **数据源扩展**：
  - 更多数据源类型支持
  - 数据转换规则引擎
  - 数据质量验证框架
- **智能分析增强**：
  - 差异自动分类
  - 问题根因智能推荐
  - 影响面自动分析

### 9.2 中长期规划

- **AI辅助分析**：引入机器学习辅助差异分析
- **云原生支持**：迁移到云原生架构
- **实时监控**：增强实时性能监控能力
- **预测性分析**：基于历史数据进行预测性分析
- **生态系统扩展**：构建更广泛的工具生态系统

### 9.3 总结

ArkReplay系统采用了现代化的技术栈和模块化设计，特别是在数据比对功能方面有深入的实现，为金融交易系统的测试和问题复现提供了强大支持。通过四层组件架构（Web、Server、Agent、DB-Agent）的协同工作，系统能够高效处理大规模交易数据，实现精准的回放控制和全面的比对分析，为金融交易系统的质量保障提供可靠支持。