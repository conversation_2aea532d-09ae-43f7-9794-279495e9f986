# core/models.py
import json
from enum import Enum
from dataclasses import dataclass
from typing import Any, Optional


@dataclass
class Record:
    """代表一条数据记录，使用__slots__优化内存使用。"""
    __slots__ = ['key', 'value']
    key: str
    value: Any


@dataclass
class DiffResult:
    """代表一条比对差异的结果。"""
    key: str
    status: str
    value_a: Optional[Any] = None
    value_b: Optional[Any] = None

    def __post_init__(self):
        if hasattr(self.status, 'value'):
            self.status = self.status.value

def create_comparison_record(
    task_id: str,
    table_name: str,
    record_key: Any,
    status: Any,
    field_name: Optional[str] = None,
    source_value: Optional[Any] = None,
    target_value: Optional[Any] = None
) -> dict:
    """
    直接创建符合ComparisonResult数据库模型要求的字典格式

    Args:
        task_id: 任务ID
        table_name: 表名
        record_key: 记录键
        status: 差异状态（DiffStatus枚举或字符串）
        field_name: 字段名（可选）
        source_value: 源值（可选）
        target_value: 目标值（可选）

    Returns:
        符合ComparisonResult模型的字典
    """
    # 处理status字段 - 确保使用枚举值
    if hasattr(status, 'value'):
        status_value = status.value
    elif isinstance(status, str) and status.startswith('DiffStatus.'):
        # 处理字符串形式的枚举名称
        from models.sqlalchemy_models import DiffStatus
        enum_name = status.replace('DiffStatus.', '')
        status_mapping = {
            'IDENTICAL': DiffStatus.IDENTICAL.value,      # 'ID'
            'DIFFERENT': DiffStatus.DIFFERENT.value,      # 'DF'
            'SOURCE_ONLY': DiffStatus.SOURCE_ONLY.value,  # 'SO'
            'TARGET_ONLY': DiffStatus.TARGET_ONLY.value,  # 'TO'
            'FIELD_DIFF': DiffStatus.FIELD_DIFF.value     # 'FD'
        }
        status_value = status_mapping.get(enum_name, status[:2])
    else:
        status_value = str(status)[:2] if status else ''

    # 处理record_key字段 - 确保是字符串
    if isinstance(record_key, dict):
        try:
            key_str = json.dumps(record_key, ensure_ascii=False)
        except (TypeError, ValueError):
            key_str = str(record_key)
    elif record_key is not None:
        key_str = str(record_key)
    else:
        key_str = ''

    # 处理value字段 - 将字典转换为JSON字符串
    def process_value(value):
        if value is None:
            return None
        elif isinstance(value, dict):
            try:
                return json.dumps(value, ensure_ascii=False)
            except (TypeError, ValueError):
                return str(value)
        else:
            return str(value)

    return {
        'task_id': str(task_id),
        'table_name': str(table_name),
        'record_key': key_str,
        'status': status_value,
        'field_name': str(field_name) if field_name is not None else None,
        'source_value': process_value(source_value),
        'target_value': process_value(target_value)
    }