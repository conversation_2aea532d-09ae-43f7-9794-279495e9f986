"""
SQLAlchemy业务逻辑层
本模块专注于业务逻辑实现，实现关注点分离的架构设计。
"""
import io
import sys
import csv
import uuid
import time
import base64
import logging
import threading
from pathlib import Path
from decimal import Decimal
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from contextlib import contextmanager

from sqlalchemy.orm import joinedload
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import and_, inspect, desc, func, text

project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
from models.pydantic_models import TaskExecutionInfo
from models.sqlalchemy_models import (
    User, UserSession, ComparisonConnection, ComparisonModel,
    ComparisonTableRule, ComparisonTask, ComparisonResult,
    TaskStatus, DiffStatus, build_conn_engine,
    create_database_engine, create_tables, get_session_factory
)

logger = logging.getLogger(__name__)


class SQLAlchemyComparisonService:
    """SQLAlchemy比对服务 - 单例模式"""
    _instance = None
    _instance_url = None
    _lock = threading.Lock()

    def __init__(self, database_url: str = None):
        # 避免重复初始化，如果已初始化则直接返回
        if hasattr(self, '_initialized'):
            return

        if database_url is None:
            database_url = self.get_default_database_url()

        # 存储数据库URL用于调试和日志
        self.database_url = database_url
        self.database_type = self.detect_database_type(database_url)

        self.engine = create_database_engine(database_url)
        self.SessionLocal = get_session_factory(self.engine)
        self.ensure_tables_initialized()

        # 初始化和进度跟踪控制
        self._initialized = True
        self._last_progress_update = {}
        self._progress_update_interval = 3.0

        logger.info(f"SQLAlchemy服务初始化完成: {database_url}")

    @classmethod
    def get_instance(cls, reporter_config: Dict[str, Any] = None) -> 'SQLAlchemyComparisonService':
        """获取单例实例"""
        database_url = cls.get_database_url_from_config(reporter_config)
        if cls._instance and cls._instance_url == database_url:
            return cls._instance

        with cls._lock:
            if cls._instance is None or cls._instance_url != database_url:
                cls._instance = cls(database_url)
                cls._instance_url = database_url
        
        return cls._instance

    @staticmethod
    def get_database_url_from_config(reporter_config: Dict[str, Any] = None) -> str:
        """从报告器配置中提取或生成数据库URL"""
        if 'configurations' in reporter_config:
            if 'postgres' in reporter_config['configurations']:
                pg_config = reporter_config['configurations']['postgres']
                user = pg_config.get('user', pg_config.get('username'))
                dbname = pg_config.get('dbname', pg_config.get('database'))
                password = pg_config.get('password', '')
                host = pg_config.get('host', 'localhost')
                port = pg_config.get('port', 5432)
                return f"postgresql+psycopg2://{user}:{password}@{host}:{port}/{dbname}"

            elif 'sqlite' in reporter_config['configurations']:
                sqlite_config = reporter_config['configurations']['sqlite']
                return f"sqlite:///{sqlite_config.get('db_path', 'sqlcompare_v4.db')}"

        elif '://' in reporter_config:
            return reporter_config
        
        return SQLAlchemyComparisonService.get_default_database_url()

    @staticmethod
    def get_default_database_url() -> str:
        default_url = "sqlite:///sqlcompare_v4.db"
        return default_url

    @staticmethod
    def detect_database_type(database_url: str) -> str:
        """检测数据库类型"""
        if database_url.startswith('postgresql'):
            return 'postgresql'
        elif database_url.startswith('mysql'):
            return 'mysql'
        elif database_url.startswith('sqlite'):
            return 'sqlite'
        elif database_url.startswith('oracle'):
            return 'oracle'
        elif database_url.startswith('db2'):
            return 'db2'
        else:
            return 'unknown'

    def ensure_tables_initialized(self):
        """确保数据库表已初始化"""
        try:           
            inspector = inspect(self.engine)
            existing_tables = inspector.get_table_names()

            # 检查关键表是否存在
            required_tables = ['comparison_models', 'comparison_results', 'comparison_table_rules', 'comparison_tasks']
            tables_initialized = all(table in existing_tables for table in required_tables)

            if not tables_initialized:
                create_tables(self.engine)

        except Exception as e:
            logger.error(f"数据库表初始化失败: {e}")
            raise

    def get_task_details(self, task_id: str) -> Optional[ComparisonTask]:
        """获取任务详细信息"""
        with self.get_db_session() as session:
            task = session.query(ComparisonTask).filter(ComparisonTask.task_id == task_id).first()

            if task:
                # 使用expunge将对象从会话中分离，但保留已加载的属性
                session.expunge(task)

            return task

    def get_task_execution_info(self, task_id: str):
        """获取任务执行信息"""
        if not task_id:
            raise ValueError("任务ID不能为空")

        try:
            query_start_time = time.perf_counter()
            with self.get_db_session() as session:
                # 一次性预加载所有相关数据，避免N+1查询问题
                task = session.query(ComparisonTask).options(
                    joinedload(ComparisonTask.model, innerjoin=False).joinedload(ComparisonModel.source_conn, innerjoin=False),
                    joinedload(ComparisonTask.model, innerjoin=False).joinedload(ComparisonModel.target_conn, innerjoin=False),
                    joinedload(ComparisonTask.model, innerjoin=False).joinedload(ComparisonModel.table_rules, innerjoin=False)
                ).filter(ComparisonTask.task_id == task_id).first()

                query_time = time.perf_counter() - query_start_time
                logger.debug(f"任务执行信息查询耗时: {query_time:.3f}秒 (任务ID: {task_id})")

                if not task or not task.model:
                    logger.warning(f"获取任务执行信息不存在: {task_id}")
                    return None

                # 处理表规则信息
                table_rules = []
                if hasattr(task.model, 'table_rules') and task.model.table_rules:
                    for rule in task.model.table_rules:
                        if rule.is_active:
                            table_rules.append({
                                'table_name': rule.table_name,
                                'remark': rule.remark,
                                'source_sql': rule.sql_1,
                                'target_sql': rule.sql_2
                            })

                # 计算匹配记录数
                matched_records = max(0, (task.total_records or 0) - (task.diff_records or 0) - (task.source_only or 0) - (task.target_only or 0))

                # 构建任务执行信息               
                execution_info = TaskExecutionInfo(
                    task_id=task.task_id,
                    task_name=task.task_name,
                    user_id=task.user_id,
                    model_id=task.model_id,
                    status=task.status,
                    progress_pct=task.progress_pct or 0,
                    current_step=task.current_step,
                    create_time=task.create_time,
                    start_time=task.start_time,
                    complete_time=task.complete_time,
                    update_time=task.update_time,
                    duration=None,
                    total_records=task.total_records or 0,
                    processed_records=task.processed_records or 0,
                    matched_records=matched_records,
                    diff_records=task.diff_records or 0,
                    source_only=task.source_only or 0,
                    target_only=task.target_only or 0,
                    exec_time=task.exec_time or 0,
                    error_msg=task.error_msg,
                    config_summary=None,
                    model=task.model,
                    table_rules=table_rules
                )

                return execution_info

        except SQLAlchemyError as e:
            logger.error(f"获取任务执行信息时数据库错误: {e}")
            raise
        except Exception as e:
            logger.error(f"获取任务执行信息时发生未知错误: {e}")
            raise

    @contextmanager
    def get_db_session(self):
        """数据库会话上下文管理器"""

        session = self.SessionLocal()
        try:
            yield session
            session.commit()
        except SQLAlchemyError as e:
            session.rollback()
            logger.error(f"数据库操作错误: {e}")
            raise
        except Exception as e:
            session.rollback()
            logger.error(f"未知错误: {e}")
            raise
        finally:
            session.close()

    # ==================== 用户管理 ====================

    def create_user(self, username: str, email: str, password: str, role: str = 'user') -> str:
        """创建用户"""
        with self.get_db_session() as session:
            # 生成唯一的user_id
            user_id = f"u_{hash(username) % 1000000:06d}"

            user = User(
                user_id=user_id,
                username=username,
                email=email,
                password=password,
                role=role
            )
            session.add(user)
            session.flush()
            return user.user_id
    
    def authenticate_user(self, email: str, password: str) -> Optional[User]:
        """用户认证"""
        with self.get_db_session() as session:
            user = session.query(User).filter(
                and_(User.email == email, User.is_active == True)
            ).first()
            
            if user and user.password == password:
                # 更新最后登录时间
                user.last_login_at = datetime.now()
                return user
            return None
    
    def create_user_session(self, user_id: str, ip_address: str, user_agent: str) -> str:
        """创建用户会话"""
        with self.get_db_session() as session:
            session_id = str(uuid.uuid4())
            expires_at = datetime.now() + timedelta(hours=24)  # 24小时过期
            
            user_session = UserSession(
                session_id=session_id,
                user_id=user_id,
                ip_address=ip_address,
                user_agent=user_agent,
                expires_at=expires_at
            )
            session.add(user_session)
            return session_id
    
    # ==================== 连接管理 ====================
    
    def create_database_connection(self, name: str, type: str, host: str,
                                 port: int, username: str, password: str,
                                 database: str, **kwargs) -> int:
        """创建数据库连接配置"""
        # 输入验证
        if not all([name, type, host, username, password, database]):
            raise ValueError("所有必需参数都不能为空")
        if port <= 0 or port > 65535:
            raise ValueError("端口号必须在1-65535范围内")

        with self.get_db_session() as session:
            # 检查连接名称是否已存在
            existing = session.query(ComparisonConnection).filter_by(name=name).first()
            if existing:
                raise ValueError(f"连接名称 '{name}' 已存在")

            connection = ComparisonConnection(
                name=name,
                type=type.lower(),
                host=host,
                port=port,
                username=username,
                password=base64.b64encode(password.encode('utf-8')).decode('utf-8'),
                database=database,
                params=kwargs.get('params')
            )
            session.add(connection)
            session.flush()

            return connection.id
    
    def get_connection_by_name(self, name: str) -> Optional[ComparisonConnection]:
        """ 根据名称查询数据库连接 """
        with self.get_db_session() as session:
            connection = session.query(ComparisonConnection).filter_by(name=name).first()
            if connection:
                session.expunge(connection)
                # 解密密码
                if connection.password:
                    connection.password = base64.b64decode(connection.password).decode('utf-8')
            return connection

    def test_database_connection(self, connection_id: int) -> Dict[str, Any]:
        """ 使用SQLAlchemy直接测试数据库连接"""
        with self.get_db_session() as session:
            connection = session.query(ComparisonConnection).filter_by(id=connection_id).first()
            if not connection:
                return {'success': False, 'error': f'连接配置不存在: {connection_id}'}

            try:
                from sqlalchemy import text
                from sqlalchemy.exc import SQLAlchemyError
                
                test_success = False
                error_message = None
                start_time = time.time()

                # 构建数据库连接引擎
                engine = build_conn_engine(
                    db_type=connection.type,
                    host=connection.host,
                    port=connection.port,
                    username=connection.username,
                    password=base64.b64decode(connection.password).decode('utf-8'),
                    database=connection.database
                )
                if not engine:
                    return { 'success': False, 'error': '不支持的数据库类型', 'connection_id': connection_id}

                try:
                    # 获取连接并执行测试查询
                    with engine.connect() as conn:
                        test_query = self._get_test_query(connection.type)
                        result = conn.execute(text(test_query))

                        row = result.fetchone()
                        if row is not None:
                            test_success = True
                        else:
                            error_message = "测试查询未返回结果"

                except SQLAlchemyError as e:
                    error_message = f"SQLAlchemy错误: {str(e)}"
                    logger.error(f"数据库连接测试失败 [{connection.name}]: {error_message}")

                except Exception as e:
                    error_message = f"连接错误: {str(e)}"
                    logger.error(f"数据库连接测试异常 [{connection.name}]: {error_message}")

                finally:
                    # 确保引擎资源被释放
                    try:
                        engine.dispose()
                    except Exception as e:
                        logger.warning(f"释放数据库引擎资源时出错: {e}")

                response_time = time.time() - start_time
                # 更新连接状态
                connection.status = 'active' if test_success else 'error'

                if test_success:
                    return {'success': True, 'connection_id': connection_id,'response_time': round(response_time, 3)}
                else:
                    return {'success': False, 'error': error_message or '未知连接错误', 'connection_id': connection_id}

            except Exception as e:
                connection.status = 'error'
                error_msg = f"连接测试异常: {str(e)}"
                return {'success': False, 'error': error_msg, 'connection_id': connection_id}

    def _get_test_query(self, db_type: str) -> str:
        """获取数据库测试查询语句"""
        db_type_lower = db_type.lower()

        if db_type_lower == 'mysql':
            return "SELECT 1 as test_result"
        elif db_type_lower == 'db2':
            return "SELECT 1 as test_result FROM SYSIBM.SYSDUMMY1"
        elif db_type_lower in ['postgresql', 'postgres']:
            return "SELECT 1 as test_result"
        elif db_type_lower == 'sqlite':
            return "SELECT 1 as test_result"
        elif db_type_lower in ['oracle', 'ora']:
            return "SELECT 1 as test_result FROM DUAL"
        elif db_type_lower in ['sqlserver', 'mssql']:
            return "SELECT 1 as test_result"
        else:
            return "SELECT 1 as test_result"

    def _get_or_create_user(self, session, user_name: str) -> User:
        """获取或创建用户"""
        user = session.query(User).filter_by(username=user_name).first()
        if user:
            return user

        user_id = f"U{uuid.uuid4().hex[:8].upper()}"
        new_user = User(
            user_id=user_id,
            username=user_name,
            email=f"{user_name}@example.com",
            password="auto_created_password" 
        )
        session.add(new_user)
        session.flush()
        logger.info(f"成功创建新用户: {user_name} (ID: {user_id})")
        return new_user
    
    # ==================== 结果管理 ====================
    def get_comparison_results(self, task_id: str, difference_type: Optional[str] = None,
                             limit: int = 100, offset: int = 0) -> List[ComparisonResult]:
        """获取比对结果 - 展示复杂查询和分页"""
        with self.get_db_session() as session:
            query = session.query(ComparisonResult).filter_by(task_id=task_id)
            
            if difference_type:
                query = query.filter(ComparisonResult.difference_type == difference_type)
            
            results = query.order_by(ComparisonResult.created_at).offset(offset).limit(limit).all()
            return results
    
    def get_task_statistics(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务统计 - 展示聚合查询"""
        with self.get_db_session() as session:
            # 使用SQLAlchemy的聚合函数
            stats = session.query(
                func.count(ComparisonResult.id).label('total_differences'),
                func.count(ComparisonResult.id).filter(
                    ComparisonResult.difference_type == DiffStatus.DIFFERENT.value
                ).label('different_records'),
                func.count(ComparisonResult.id).filter(
                    ComparisonResult.difference_type == DiffStatus.SOURCE_ONLY.value
                ).label('source_only_records'),
                func.count(ComparisonResult.id).filter(
                    ComparisonResult.difference_type == DiffStatus.TARGET_ONLY.value
                ).label('target_only_records')
            ).filter(ComparisonResult.task_id == task_id).first()
            
            if stats:
                return {
                    'total_differences': stats.total_differences,
                    'different_records': stats.different_records,
                    'source_only_records': stats.source_only_records,
                    'target_only_records': stats.target_only_records
                }
            return None
    
    # ==================== 高级查询 ====================
    
    def get_user_task_summary(self, user_id: str) -> Dict[str, Any]:
        """获取用户任务摘要 - 展示复杂的关联查询"""
        with self.get_db_session() as session:
            # 复杂的关联查询
            summary = session.query(
                func.count(ComparisonTask.id).label('total_tasks'),
                func.count(ComparisonTask.id).filter(
                    ComparisonTask.status == TaskStatus.COMPLETED.value
                ).label('completed_tasks'),
                func.count(ComparisonTask.id).filter(
                    ComparisonTask.status == TaskStatus.RUNNING.value
                ).label('running_tasks'),
                func.count(ComparisonTask.id).filter(
                    ComparisonTask.status == TaskStatus.FAILED.value
                ).label('failed_tasks'),
                func.avg(ComparisonTask.exec_time).label('avg_execution_time'),
                func.sum(ComparisonTask.diff_records).label('total_differences')
            ).filter(ComparisonTask.user_id == user_id).first()
            
            return {
                'total_tasks': summary.total_tasks or 0,
                'completed_tasks': summary.completed_tasks or 0,
                'running_tasks': summary.running_tasks or 0,
                'failed_tasks': summary.failed_tasks or 0,
                'avg_execution_time': float(summary.avg_execution_time or 0),
                'total_differences': summary.total_differences or 0
            }

    # ==================== 任务管理 ====================

    def _generate_standard_task_name(self) -> str:
        """生成标准化的任务名称"""
        with self.get_db_session() as session:
            # 优化查询，移除冗余条件
            query = text("""
                SELECT task_name FROM comparison_tasks
                WHERE task_name LIKE 'TASK_%%'
                AND LENGTH(task_name) = 8
                ORDER BY task_name DESC
                LIMIT 1
            """)
            result = session.execute(query).fetchone()

            if result and result[0]:
                # 提取序列号并加1
                last_task_name = result[0]
                last_sequence = int(last_task_name.split('_')[1])
                next_sequence = last_sequence + 1
            else:
                # 如果没有找到符合格式的任务，从001开始
                next_sequence = 1

            # 生成新的任务名称，序列号补零到3位
            new_task_name = f"TASK_{next_sequence:03d}"

            # 确保生成的任务名称在数据库中是唯一的
            while session.query(ComparisonTask).filter_by(task_name=new_task_name).first():
                next_sequence += 1
                new_task_name = f"TASK_{next_sequence:03d}"

            return new_task_name

    def create_task_from_model(self, user_name: str, model_id: int, task_name: str = None) -> str:
        """创建比对任务 - 支持标准化任务命名"""
        with self.get_db_session() as session:
            # 验证用户存在，如果不存在则自动创建
            user = self._get_or_create_user(session, user_name)

            # 如果未指定任务名称或为空，则自动生成
            if not task_name or task_name.strip() == "":
                task_name = self._generate_standard_task_name()
            else:
                task_name = task_name.strip()

            # 生成任务ID
            task_id = datetime.now().strftime("A%Y%m%d%H%M%S")

            # 创建任务
            task = ComparisonTask(
                task_id=task_id,
                user_id=user.user_id,
                model_id=model_id,
                task_name=task_name,
                status=TaskStatus.PENDING.value
            )
            session.add(task)
            session.flush()

            logger.info(f"创建比对任务成功: {task_name} ({task_id})")
            return task_id

    def create_task_from_direct(self, user_name: str, task_data) -> str:
        """直接创建任务"""
        with self.get_db_session() as session:
            user = self._get_or_create_user(session, user_name)

            # 提取任务名称，支持标准化命名
            if not hasattr(task_data, 'task_name') or not task_data.task_name or task_data.task_name.strip() == "":
                task_name = self._generate_standard_task_name()
            else:
                task_name = task_data.task_name

            # 生成任务ID
            task_id = datetime.now().strftime("A%Y%m%d%H%M%S")

            # 创建任务
            task = ComparisonTask(
                task_id=task_id,
                user_id=user.user_id,
                model_id=None,
                task_name=task_name,
                status=TaskStatus.PENDING.value
            )
            session.add(task)
            session.flush()

            logger.info(f"TaskCreateDirect任务创建成功: {task_name} ({task_id})")
            return task_id

    def get_user_tasks(self, user_name: str, status_filter: Optional[str] = None,
                      limit: int = 20, offset: int = 0) -> List[ComparisonTask]:
        """获取用户任务列表 - 展示复杂查询功能"""
        with self.get_db_session() as session:
            query = session.query(ComparisonTask).filter_by(user_name=user_name)
            
            # 状态过滤
            if status_filter:
                query = query.filter(ComparisonTask.status == status_filter)
            
            # 预加载关联数据
            query = query.options(
                joinedload(ComparisonTask.user),
                joinedload(ComparisonTask.results)
            )
            
            # 排序和分页
            tasks = query.order_by(desc(ComparisonTask.create_time)).offset(offset).limit(limit).all()
            return tasks
    
    def update_task_status(self, task_id: str, status: TaskStatus, **kwargs) -> bool:
        """更新任务状态"""
        with self.get_db_session() as session:
            task = session.query(ComparisonTask).filter_by(task_id=task_id).first()
            if not task:
                return False

            # 更新状态
            task.status = status.value

            # 更新其他字段
            for key, value in kwargs.items():
                if hasattr(task, key):
                    setattr(task, key, value)

            # 设置时间戳（修复字段名）
            if status == TaskStatus.RUNNING and not task.start_time:
                task.start_time = datetime.now()
            elif status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                task.complete_time = datetime.now()

            return True

    def update_task_progress(self, task_id: str, progress_pct: float, current_step: str = None,
                           processed_records: int = None, status: TaskStatus = None, **kwargs) -> bool:
        """
        更新任务进度信息（兼容任务状态更新）

        Args:
            task_id: 任务ID
            progress_pct: 进度百分比 (0.0-100.0)
            current_step: 当前执行步骤描述
            processed_records: 已处理记录数
            status: 任务状态（可选，如果提供则同时更新状态）
            **kwargs: 其他需要更新的字段

        Returns:
            bool: 更新是否成功
        """
        try:
            with self.get_db_session() as session:
                task = session.query(ComparisonTask).filter_by(task_id=task_id).first()
                if not task:
                    return False

                # 更新进度信息
                task.progress_pct = Decimal(str(min(100.0, max(0.0, progress_pct))))
                if current_step:
                    task.current_step = current_step
                if processed_records is not None:
                    task.processed_records = processed_records

                # 更新任务状态（如果提供）
                if status is not None:
                    task.status = status.value
                    # 设置状态相关的时间戳
                    if status == TaskStatus.RUNNING and not task.start_time:
                        task.start_time = datetime.now()
                    elif status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                        task.complete_time = datetime.now()

                # 更新其他字段
                for key, value in kwargs.items():
                    if hasattr(task, key):
                        setattr(task, key, value)

                # 更新时间戳
                task.update_time = datetime.now()

                session.commit()
                return True

        except Exception as e:
            logger.error(f"更新任务进度失败: {task_id}, 错误: {e}")
            return False

    def get_task_progress(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取任务进度信息

        Args:
            task_id: 任务ID

        Returns:
            Dict: 包含进度信息的字典，如果任务不存在则返回None
        """
        try:
            with self.get_db_session() as session:
                task = session.query(ComparisonTask).filter_by(task_id=task_id).first()
                if not task:
                    return None

                return {
                    'task_id': task.task_id,
                    'task_name': task.task_name,
                    'status': task.status,
                    'progress_pct': float(task.progress_pct or 0),
                    'current_step': task.current_step,
                    'processed_records': task.processed_records or 0,
                    'total_records': task.total_records or 0,
                    'create_time': task.create_time,
                    'start_time': task.start_time,
                    'update_time': task.update_time,
                    'estimated_completion': None  # 可以根据进度和已用时间计算
                }

        except Exception as e:
            logger.error(f"获取任务进度失败: {task_id}, 错误: {e}")
            return None

    def get_task_summary(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务汇总信息（从service_models.py迁移的业务逻辑）"""
        with self.get_db_session() as session:
            task = session.query(ComparisonTask).filter_by(task_id=task_id).first()
            if not task:
                return None

            # 计算匹配记录数
            matched_records = (task.total_records or 0) - (task.diff_records or 0) - (task.source_only or 0) - (task.target_only or 0)

            return {
                'task_id': task_id,
                'task_name': task.task_name,
                'description': getattr(task, 'description', None),  # ComparisonTask没有description字段
                'user_id': task.user_id,
                'model_id': task.model_id,
                'status': task.status,
                'progress_pct': float(task.progress_pct or 0),
                'current_step': task.current_step,
                'create_time': task.create_time,
                'start_time': task.start_time,
                'complete_time': task.complete_time,
                'update_time': task.update_time,
                'total_records': task.total_records or 0,
                'processed_records': task.processed_records or 0,
                'matched_records': max(0, matched_records),
                'diff_records': task.diff_records or 0,
                'source_only': task.source_only or 0,
                'target_only': task.target_only or 0,
                'exec_time': float(task.exec_time or 0),
                'error_msg': task.error_msg
            }

    def list_connection_configs(self, type: str = None, status: str = None) -> List[Dict[str, Any]]:
        """获取连接配置列表"""
        with self.get_db_session() as session:
            query = session.query(ComparisonConnection)

            if type:
                query = query.filter(ComparisonConnection.type == type.lower())
            if status:
                query = query.filter(ComparisonConnection.status == status)

            connections = query.order_by(desc(ComparisonConnection.create_time)).all()

            return [
                {
                    'id': conn.id,
                    'name': conn.name,
                    'type': conn.type,
                    'host': conn.host,
                    'port': conn.port,
                    'database': conn.database,
                    'status': conn.status,
                    'created_at': conn.create_time.isoformat() if conn.create_time else None
                }
                for conn in connections
            ]

    # ==================== 模型管理方法 ====================

    def create_comparison_model(self, name: str, description: str = None, source_connid: int = None, target_connid: int = None,
                               cmp_type: str = "content", global_config: Dict[str, Any] = None) -> int:
        """创建比对模型"""
        try:
            with self.get_db_session() as session:
                # 检查模型名称是否已存在
                existing_model = session.query(ComparisonModel).filter(ComparisonModel.name == name).first()
                if existing_model:
                    return existing_model.id

                # 验证连接是否存在
                if source_connid:
                    source_conn = session.query(ComparisonConnection).filter(ComparisonConnection.id == source_connid).first()
                    if not source_conn:
                        raise ValueError(f"源连接不存在: {source_connid}")

                if target_connid:
                    target_conn = session.query(ComparisonConnection).filter(ComparisonConnection.id == target_connid).first()
                    if not target_conn:
                        raise ValueError(f"目标连接不存在: {target_connid}")

                # 创建比对模型
                model = ComparisonModel(
                    name=name,
                    description=description,
                    source_connid=source_connid,
                    target_connid=target_connid,
                    cmp_type=cmp_type,
                    global_config=global_config or {}
                )

                session.add(model)
                session.commit()

                logger.info(f"比对模型创建成功: {name}, ID: {model.id}")
                return model.id

        except Exception as e:
            logger.error(f"创建比对模型失败: {e}", exc_info=True)
            raise

    def get_comparison_model(self, model_id: int) -> Optional[Dict[str, Any]]:
        """获取比对模型信息"""
        try:
            with self.get_db_session() as session:
                model = session.query(ComparisonModel).filter(ComparisonModel.id == model_id).first()

                if not model:
                    return None

                return {
                    'id': model.id,
                    'name': model.name,
                    'description': model.description,
                    'source_connid': model.source_connid,
                    'target_connid': model.target_connid,
                    'cmp_type': model.cmp_type,
                    'global_config': model.global_config,
                    'create_time': model.create_time.isoformat() if model.create_time else None,
                    'update_time': model.update_time.isoformat() if model.update_time else None
                }

        except Exception as e:
            logger.error(f"获取比对模型失败: {model_id}, 错误: {e}")
            return None

    def get_comparison_model_by_name(self, name: str) -> Optional[ComparisonModel]:
        """根据名称查询比对模型"""
        try:
            with self.get_db_session() as session:
                model = session.query(ComparisonModel).filter(ComparisonModel.name == name).first()

                if model:
                    # 将对象从会话中分离，避免会话关闭后访问问题
                    session.expunge(model)

                return model

        except Exception as e:
            logger.error(f"根据名称查询比对模型失败: {name}, 错误: {e}")
            return None

    def create_table_rule(self, model_id: int, table_id: str = None, table_name: str = None,
                         sql_1: str = None, sql_2: str = None, remark: str = None,
                         primary_keys: List[str] = None, ignore_fields: List[str] = None,
                         field_mappings: Dict[str, str] = None) -> int:
        """创建表规则"""
        try:
            with self.get_db_session() as session:
                # 验证模型是否存在
                model = session.query(ComparisonModel).filter(ComparisonModel.id == model_id).first()
                if not model:
                    raise ValueError(f"比对模型不存在: {model_id}")

                # 如果没有提供table_id，根据表名和remark生成哈希ID
                if not table_id:
                    table_id = self._generate_table_id(model_id, table_name, remark)

                # 检查table_id在同一模型内是否已存在
                existing_rule = session.query(ComparisonTableRule).filter(
                    ComparisonTableRule.model_id == model_id,
                    ComparisonTableRule.table_id == table_id
                ).first()

                if existing_rule:
                    logger.info(f"表规则已存在: {table_id}, ID: {existing_rule.id}")
                    return existing_rule.id

                # 创建表规则
                table_rule = ComparisonTableRule(
                    model_id=model_id,
                    table_id=table_id,
                    table_name=table_name or table_id,
                    sql_1=sql_1,
                    sql_2=sql_2,
                    remark=remark,
                    primary_keys=primary_keys or [],
                    ignore_fields=ignore_fields or [],
                    field_mappings=field_mappings or {}
                )

                session.add(table_rule)
                session.commit()

                logger.info(f"表规则创建成功: {table_name or table_id}, ID: {table_rule.id}")
                return table_rule.id

        except Exception as e:
            logger.error(f"创建表规则失败: {e}", exc_info=True)
            raise

    def _generate_table_id(self, model_id: int, table_name: str, remark: str = None) -> str:
        """根据表名和备注生成唯一的table_id"""
        import hashlib

        # 构建哈希输入字符串
        hash_input = f"{table_name or 'unknown'}"
        if remark:
            hash_input += f"_{remark}"

        # 计算MD5哈希
        hash_object = hashlib.md5(hash_input.encode('utf-8'))
        hash_hex = hash_object.hexdigest().upper()

        # 取前8位作为table_id，并添加模型ID前缀确保跨模型唯一性
        table_id = f"M{model_id}{hash_hex[:8]}"
        return table_id

    def get_table_rule(self, rule_id: int) -> Optional[Dict[str, Any]]:
        """获取表规则信息"""
        try:
            with self.get_db_session() as session:
                rule = session.query(ComparisonTableRule).filter(ComparisonTableRule.id == rule_id).first()

                if not rule:
                    return None

                return {
                    'id': rule.id,
                    'model_id': rule.model_id,
                    'table_id': rule.table_id,
                    'table_name': rule.table_name,
                    'sql_1': rule.sql_1,
                    'sql_2': rule.sql_2,
                    'remark': rule.remark,
                    'primary_keys': rule.primary_keys,
                    'ignore_fields': rule.ignore_fields,
                    'field_mappings': rule.field_mappings,
                    'is_active': rule.is_active,
                    'create_time': rule.create_time.isoformat() if rule.create_time else None,
                    'update_time': rule.update_time.isoformat() if rule.update_time else None
                }

        except Exception as e:
            logger.error(f"获取表规则失败: {rule_id}, 错误: {e}")
            return None

    def get_model_table_rules(self, model_id: int) -> List[Dict[str, Any]]:
        """
        获取模型下的所有表规则

        Args:
            model_id: 模型ID

        Returns:
            List[Dict[str, Any]]: 表规则列表
        """
        try:
            with self.get_db_session() as session:
                rules = session.query(ComparisonTableRule).filter(
                    ComparisonTableRule.model_id == model_id,
                    ComparisonTableRule.is_active == True
                ).all()

                rules_list = []
                for rule in rules:
                    rules_list.append({
                        'id': rule.id,
                        'table_id': rule.table_id,
                        'table_name': rule.table_name,
                        'sql_1': rule.sql_1,
                        'sql_2': rule.sql_2,
                        'remark': rule.remark,
                        'primary_keys': rule.primary_keys,
                        'ignore_fields': rule.ignore_fields,
                        'field_mappings': rule.field_mappings,
                        'is_active': rule.is_active,
                        'create_time': rule.create_time.isoformat() if rule.create_time else None,
                        'update_time': rule.update_time.isoformat() if rule.update_time else None
                    })

                return rules_list

        except Exception as e:
            logger.error(f"获取模型表规则失败: {model_id}, 错误: {e}")
            return []

    # ==================== 删除操作方法 ====================

    def delete_task(self, task_id: str) -> bool:
        """删除任务及其相关数据"""
        try:
            with self.get_db_session() as session:
                # 查找任务
                task = session.query(ComparisonTask).filter(ComparisonTask.task_id == task_id).first()

                if not task:
                    logger.warning(f"任务不存在: {task_id}")
                    return False

                # 如果任务正在运行，先更新状态为取消
                if task.status == TaskStatus.RUNNING:
                    task.status = TaskStatus.CANCELLED
                    task.update_time = datetime.now()
                    session.commit()
                    logger.info(f"任务 {task_id} 已取消")

                # 级联删除会自动删除相关的ComparisonResult记录
                session.delete(task)
                session.commit()

                logger.info(f"任务删除成功: {task_id}")
                return True

        except Exception as e:
            logger.error(f"删除任务失败: {task_id}, 错误: {e}", exc_info=True)
            return False

    def delete_user(self, user_id: str) -> bool:
        """删除用户及其相关数据"""
        try:
            with self.get_db_session() as session:
                # 查找用户
                user = session.query(User).filter(User.user_id == user_id).first()

                if not user:
                    logger.warning(f"用户不存在: {user_id}")
                    return False

                # 级联删除会自动删除相关的任务、会话等记录
                session.delete(user)
                session.commit()

                logger.info(f"用户删除成功: {user_id}")
                return True

        except Exception as e:
            logger.error(f"删除用户失败: {user_id}, 错误: {e}", exc_info=True)
            return False

    def delete_comparison_model(self, model_id: int) -> bool:
        """删除比对模型及其相关数据"""
        try:
            with self.get_db_session() as session:
                # 查找模型
                model = session.query(ComparisonModel).filter(ComparisonModel.id == model_id).first()

                if not model:
                    logger.warning(f"比对模型不存在: {model_id}")
                    return False

                # 删除模型（级联删除会自动删除相关的表规则和任务）
                session.delete(model)
                session.commit()

                logger.info(f"比对模型删除成功: {model_id}")
                return True

        except Exception as e:
            logger.error(f"删除比对模型失败: {model_id}, 错误: {e}", exc_info=True)
            return False

    def delete_database_connection(self, connection_id: int) -> bool:
        """删除数据库连接配置"""
        try:
            with self.get_db_session() as session:
                # 查找连接
                connection = session.query(ComparisonConnection).filter(
                    ComparisonConnection.id == connection_id
                ).first()

                if not connection:
                    logger.warning(f"数据库连接不存在: {connection_id}")
                    return False

                # 检查是否有模型在使用此连接
                models_using_connection = session.query(ComparisonModel).filter(
                    (ComparisonModel.source_connid == connection_id) |
                    (ComparisonModel.target_connid == connection_id)
                ).count()

                if models_using_connection > 0:
                    logger.warning(f"无法删除连接 {connection_id}: 仍有 {models_using_connection} 个模型在使用")
                    return False

                # 删除连接
                session.delete(connection)
                session.commit()

                logger.info(f"数据库连接删除成功: {connection_id}")
                return True

        except Exception as e:
            logger.error(f"删除数据库连接失败: {connection_id}, 错误: {e}", exc_info=True)
            return False

    def delete_table_rule(self, rule_id: int) -> bool:
        """删除表规则"""
        try:
            with self.get_db_session() as session:
                # 查找规则
                rule = session.query(ComparisonTableRule).filter(ComparisonTableRule.id == rule_id).first()

                if not rule:
                    logger.warning(f"表规则不存在: {rule_id}")
                    return False

                # 级联删除会自动处理相关任务
                session.delete(rule)
                session.commit()

                logger.info(f"表规则删除成功: {rule_id}")
                return True

        except Exception as e:
            logger.error(f"删除表规则失败: {rule_id}, 错误: {e}", exc_info=True)
            return False

    def cleanup_old_tasks(self, days_old: int = 30, status_filter: List[str] = None) -> int:
        """清理旧任务数据"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days_old)

            with self.get_db_session() as session:
                query = session.query(ComparisonTask).filter(ComparisonTask.create_time < cutoff_date)

                # 应用状态过滤器
                if status_filter:
                    query = query.filter(ComparisonTask.status.in_(status_filter))
                else:
                    # 默认只删除已完成、失败或取消的任务
                    query = query.filter(ComparisonTask.status.in_([
                        TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED
                    ]))

                # 获取要删除的任务
                old_tasks = query.all()
                deleted_count = len(old_tasks)

                if deleted_count == 0:
                    logger.info("没有找到需要清理的旧任务")
                    return 0

                # 批量删除
                for task in old_tasks:
                    session.delete(task)

                session.commit()

                logger.info(f"清理旧任务完成: 删除了 {deleted_count} 个任务")
                return deleted_count

        except Exception as e:
            logger.error(f"清理旧任务失败: {e}", exc_info=True)
            return 0

    # ==================== 高性能批量写入接口 ====================

    def bulk_insert_diffs(self, task_id: str, table_name: str, diff_records: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        高性能批量插入差异记录

        @deprecated: 此方法已废弃，请使用 batch_insert_comparison_results 方法
        新方法已集成了所有性能优化策略，包括PostgreSQL的COPY FROM STDIN

        兼容性说明：此方法现在内部调用 batch_insert_comparison_results

        Args:
            task_id: 任务ID
            table_name: 表名（用于日志记录，已忽略）
            diff_records: 差异记录列表

        Returns:
            Dict[str, Any]: 插入结果统计
        """
        import warnings
        warnings.warn(
            "bulk_insert_diffs已废弃，请使用batch_insert_comparison_results。"
            "新方法已集成所有性能优化策略。",
            DeprecationWarning,
            stacklevel=2
        )

        # 兼容性处理：为缺少task_id的记录添加task_id
        enhanced_records = []
        for record in diff_records:
            if 'task_id' not in record or not record['task_id']:
                record = record.copy()
                record['task_id'] = task_id
            enhanced_records.append(record)

        # 调用增强版的batch_insert_comparison_results
        return self.batch_insert_comparison_results(enhanced_records)


    def _bulk_insert_sqlite(self, task_id: str, diff_records: List[Dict[str, Any]]) -> tuple[int, str]:
        """SQLite高性能批量插入"""
        try:
            # 获取底层sqlite3连接
            raw_connection = self.engine.raw_connection()
            cursor = raw_connection.cursor()

            # 准备批量插入数据
            insert_sql = """
            INSERT INTO comparison_results
            (task_id, table_name, record_key, status, field_name, source_value, target_value)
            VALUES (?, ?, ?, ?, ?, ?, ?)
            """

            # 转换记录格式
            batch_data = []
            for record in diff_records:
                batch_data.append((
                    record.get('task_id', task_id),
                    record.get('table_name', ''),
                    record.get('record_key', ''),
                    record.get('status', ''),
                    record.get('field_name'),
                    record.get('source_value'),
                    record.get('target_value')
                ))

            # 执行批量插入
            cursor.executemany(insert_sql, batch_data)
            raw_connection.commit()

            inserted_count = len(batch_data)
            logger.debug(f"SQLite批量插入完成: {inserted_count} 条记录")

            return inserted_count, 'sqlite_executemany'

        except Exception as e:
            if 'raw_connection' in locals():
                raw_connection.rollback()
            logger.error(f"SQLite批量插入失败: {e}")
            # 回退到SQLAlchemy方法
            return self._bulk_insert_sqlalchemy(task_id, diff_records)
        finally:
            if 'raw_connection' in locals():
                raw_connection.close()

    def _bulk_insert_postgresql(self, task_id: str, diff_records: List[Dict[str, Any]]) -> tuple[int, str]:
        """PostgreSQL高性能批量插入"""
        try:
            # 尝试使用psycopg2的COPY FROM STDIN
            return self._bulk_insert_postgresql_copy(task_id, diff_records)
        except Exception as e:
            logger.warning(f"PostgreSQL COPY方法失败，回退到execute_values: {e}")
            try:
                return self._bulk_insert_postgresql_execute_values(task_id, diff_records)
            except Exception as e2:
                logger.warning(f"PostgreSQL execute_values方法失败，回退到SQLAlchemy: {e2}")
                return self._bulk_insert_sqlalchemy(task_id, diff_records)

    def _bulk_insert_postgresql_copy(self, task_id: str, diff_records: List[Dict[str, Any]]) -> tuple[int, str]:
        """PostgreSQL COPY FROM STDIN方法"""
        import io
        import csv

        # 获取底层psycopg2连接
        raw_connection = self.engine.raw_connection()
        cursor = raw_connection.cursor()

        try:
            # 准备COPY数据
            copy_data = io.StringIO()
            writer = csv.writer(copy_data, delimiter='\t', lineterminator='\n')

            for record in diff_records:
                row_data = [
                    record.get('task_id', task_id),
                    record.get('table_name', ''),
                    record.get('record_key', ''),
                    record.get('status', ''),
                    record.get('field_name'),
                    record.get('source_value'),
                    record.get('target_value')
                ]

                # 处理None值为PostgreSQL的NULL
                processed_row = ['\\N' if v is None else str(v) for v in row_data]
                writer.writerow(processed_row)

            copy_data.seek(0)

            # 执行COPY命令
            cursor.copy_from(
                copy_data,
                'comparison_results',
                columns=('task_id', 'table_name', 'record_key', 'status', 'field_name', 'source_value', 'target_value'),
                sep='\t',
                null='\\N'
            )

            raw_connection.commit()
            inserted_count = len(diff_records)

            logger.debug(f"PostgreSQL COPY插入完成: {inserted_count} 条记录")
            return inserted_count, 'postgresql_copy'

        except Exception as e:
            raw_connection.rollback()
            raise e
        finally:
            raw_connection.close()

    def _bulk_insert_postgresql_execute_values(self, task_id: str, diff_records: List[Dict[str, Any]]) -> tuple[int, str]:
        """PostgreSQL execute_values方法"""
        try:
            import psycopg2.extras

            # 获取底层psycopg2连接
            raw_connection = self.engine.raw_connection()
            cursor = raw_connection.cursor()

            # 准备批量插入数据
            insert_sql = """
            INSERT INTO comparison_results
            (task_id, table_name, record_key, status, field_name, source_value, target_value)
            VALUES %s
            """

            # 转换记录格式
            batch_data = []
            for record in diff_records:
                batch_data.append((
                    record.get('task_id', task_id),
                    record.get('table_name', ''),
                    record.get('record_key', ''),
                    record.get('status', ''),
                    record.get('field_name'),
                    record.get('source_value'),
                    record.get('target_value')
                ))

            # 执行批量插入
            psycopg2.extras.execute_values(cursor, insert_sql, batch_data, page_size=10000)
            raw_connection.commit()

            inserted_count = len(batch_data)
            logger.debug(f"PostgreSQL execute_values插入完成: {inserted_count} 条记录")

            return inserted_count, 'postgresql_execute_values'

        except Exception as e:
            if 'raw_connection' in locals():
                raw_connection.rollback()
            raise e
        finally:
            if 'raw_connection' in locals():
                raw_connection.close()

    def _bulk_insert_sqlalchemy(self, task_id: str, diff_records: List[Dict[str, Any]]) -> tuple[int, str]:
        """SQLAlchemy标准批量插入方法（回退方案）"""
        try:
            with self.get_db_session() as session:
                from models.sqlalchemy_models import ComparisonResult

                # 准备批量插入数据
                mappings = []
                for record in diff_records:
                    mapping = {
                        'task_id': record.get('task_id', task_id),
                        'table_name': record.get('table_name', ''),
                        'record_key': record.get('record_key', ''),
                        'status': record.get('status', ''),
                        'field_name': record.get('field_name'),
                        'source_value': record.get('source_value'),
                        'target_value': record.get('target_value')
                    }
                    mappings.append(mapping)

                # 执行批量插入
                session.bulk_insert_mappings(ComparisonResult, mappings)
                session.commit()

                inserted_count = len(mappings)
                logger.debug(f"SQLAlchemy批量插入完成: {inserted_count} 条记录")

                return inserted_count, 'sqlalchemy_bulk_insert_mappings'

        except Exception as e:
            logger.error(f"SQLAlchemy批量插入失败: {e}")
            raise

    def batch_insert_comparison_results(self, diff_data_list: List[Dict[str, Any]]) -> Dict[str, Any]:
        """批量插入差异数据"""
        if not diff_data_list:
            return {'inserted_count': 0, 'insert_time': 0.0, 'method': 'none'}

        start_time = time.time()
        inserted_count = 0
        method_used = 'unknown'

        try:
            # 根据数据库类型选择最优插入策略
            if self.database_type == 'sqlite':
                inserted_count, method_used = self._batch_insert_sqlite(diff_data_list)
            elif self.database_type == 'postgresql':
                inserted_count, method_used = self._batch_insert_postgresql(diff_data_list)
            else:
                inserted_count, method_used = self._batch_insert_core(diff_data_list)

            insert_time = time.time() - start_time

            return {
                'inserted_count': inserted_count,
                'insert_time': insert_time,
                'method': method_used,
                'records_per_second': inserted_count / insert_time if insert_time > 0 else 0
            }

        except Exception as e:
            insert_time = time.time() - start_time
            logger.error(f"批量插入失败: 错误={e}, 耗时={insert_time:.2f}秒")
            raise

    def _batch_insert_sqlite(self, diff_data_list: List[Dict[str, Any]]) -> tuple[int, str]:
        """SQLite高性能批量插入"""
        try:
            # 获取底层sqlite3连接
            raw_connection = self.engine.raw_connection()
            cursor = raw_connection.cursor()

            # 准备批量插入数据
            insert_sql = """
            INSERT INTO comparison_results
            (task_id, table_name, record_key, status, field_name, source_value, target_value)
            VALUES (?, ?, ?, ?, ?, ?, ?)
            """

            # 转换记录格式
            batch_data = []
            for record in diff_data_list:
                batch_data.append((
                    record.get('task_id', ''),
                    record.get('table_name', ''),
                    record.get('record_key', ''),
                    record.get('status', ''),
                    record.get('field_name'),
                    record.get('source_value'),
                    record.get('target_value')
                ))

            # 执行批量插入
            cursor.executemany(insert_sql, batch_data)
            raw_connection.commit()

            inserted_count = len(batch_data)
            logger.debug(f"SQLite批量插入完成: {inserted_count} 条记录")

            return inserted_count, 'sqlite_executemany'

        except Exception as e:
            if 'raw_connection' in locals():
                raw_connection.rollback()
            logger.error(f"SQLite批量插入失败: {e}")
            # 回退到SQLAlchemy Core方法
            return self._batch_insert_core(diff_data_list)
        finally:
            if 'raw_connection' in locals():
                raw_connection.close()

    def _batch_insert_postgresql(self, diff_data_list: List[Dict[str, Any]]) -> tuple[int, str]:
        """PostgreSQL高性能批量插入 - 增强版（3层回退策略）"""
        try:
            # 第1层：尝试使用PostgreSQL COPY FROM STDIN（最高性能）
            return self._batch_insert_postgresql_copy(diff_data_list)
        except Exception as e:
            logger.warning(f"PostgreSQL COPY方法失败，回退到execute_values: {e}")
            try:
                # 第2层：使用psycopg2.extras.execute_values（高性能）
                return self._batch_insert_postgresql_execute_values(diff_data_list)
            except Exception as e2:
                logger.warning(f"PostgreSQL execute_values方法失败，回退到SQLAlchemy Core: {e2}")
                # 第3层：回退到SQLAlchemy Core方法（标准性能）
                return self._batch_insert_core(diff_data_list)

    def _batch_insert_postgresql_copy(self, diff_data_list: List[Dict[str, Any]]) -> tuple[int, str]:
        """PostgreSQL COPY FROM STDIN方法 - 最高性能"""

        # 获取底层psycopg2连接
        raw_connection = self.engine.raw_connection()
        cursor = raw_connection.cursor()

        try:
            # 准备COPY数据
            copy_data = io.StringIO()
            writer = csv.writer(copy_data, delimiter='\t', lineterminator='\n')

            for record in diff_data_list:
                row_data = [
                    record.get('task_id', ''),
                    record.get('table_name', ''),
                    record.get('record_key', ''),
                    record.get('status', ''),
                    record.get('field_name'),
                    record.get('source_value'),
                    record.get('target_value')
                ]

                # 处理None值为PostgreSQL的NULL
                processed_row = ['\\N' if v is None else str(v) for v in row_data]
                writer.writerow(processed_row)

            copy_data.seek(0)

            # 执行COPY命令
            cursor.copy_from(
                copy_data,
                'comparison_results',
                columns=('task_id', 'table_name', 'record_key', 'status', 'field_name', 'source_value', 'target_value'),
                sep='\t',
                null='\\N'
            )

            raw_connection.commit()
            inserted_count = len(diff_data_list)

            logger.debug(f"PostgreSQL COPY插入完成: {inserted_count} 条记录")
            return inserted_count, 'postgresql_copy'

        except Exception as e:
            raw_connection.rollback()
            raise e
        finally:
            raw_connection.close()

    def _batch_insert_postgresql_execute_values(self, diff_data_list: List[Dict[str, Any]]) -> tuple[int, str]:
        """PostgreSQL execute_values方法 - 高性能"""
        try:
            import psycopg2.extras

            # 获取底层psycopg2连接
            raw_connection = self.engine.raw_connection()
            cursor = raw_connection.cursor()

            # 准备批量插入数据
            insert_sql = """
            INSERT INTO comparison_results
            (task_id, table_name, record_key, status, field_name, source_value, target_value)
            VALUES %s
            """

            # 转换记录格式
            batch_data = []
            for record in diff_data_list:
                batch_data.append((
                    record.get('task_id', ''),
                    record.get('table_name', ''),
                    record.get('record_key', ''),
                    record.get('status', ''),
                    record.get('field_name'),
                    record.get('source_value'),
                    record.get('target_value')
                ))

            # 执行批量插入
            psycopg2.extras.execute_values(cursor, insert_sql, batch_data, page_size=10000)
            raw_connection.commit()

            inserted_count = len(batch_data)
            logger.debug(f"PostgreSQL execute_values插入完成: {inserted_count} 条记录")

            return inserted_count, 'postgresql_execute_values'

        except Exception as e:
            if 'raw_connection' in locals():
                raw_connection.rollback()
            raise e
        finally:
            if 'raw_connection' in locals():
                raw_connection.close()

    def _batch_insert_core(self, diff_data_list: List[Dict[str, Any]]) -> tuple[int, str]:
        """SQLAlchemy Core批量插入方法（通用回退方案）"""
        try:
            from models.sqlalchemy_models import ComparisonResult
            from sqlalchemy import text

            # 使用SQLAlchemy Core的connection.execute
            with self.engine.connect() as connection:
                # 准备批量插入数据
                insert_data = []
                for record in diff_data_list:
                    insert_data.append({
                        'task_id': record.get('task_id', ''),
                        'table_name': record.get('table_name', ''),
                        'record_key': record.get('record_key', ''),
                        'status': record.get('status', ''),
                        'field_name': record.get('field_name'),
                        'source_value': record.get('source_value'),
                        'target_value': record.get('target_value')
                    })

                # 执行批量插入
                connection.execute(ComparisonResult.__table__.insert(), insert_data)
                connection.commit()

                inserted_count = len(insert_data)
                logger.debug(f"SQLAlchemy Core批量插入完成: {inserted_count} 条记录")

                return inserted_count, 'sqlalchemy_core'

        except Exception as e:
            logger.error(f"SQLAlchemy Core批量插入失败: {e}")
            raise


