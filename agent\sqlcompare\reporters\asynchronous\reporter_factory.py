# reporters/asynchronous/reporter_factory.py
import logging
from typing import List, Dict, Any, Optional
from .base_reporter import AsyncBaseReporter
from .csvfile_reporter import AsyncFilePairReporter

logger = logging.getLogger(__name__)

class AsyncReporterFactory:
    """异步报告器工厂，用于根据配置创建异步报告器实例"""

    @staticmethod
    async def create_reporter(reporter_config: Dict[str, Any], task_id: str) -> Optional[AsyncBaseReporter]:
        """根据配置创建异步报告器实例"""
        reporter_type = reporter_config.get('type')
        try:
            if reporter_type in ['csv', 'file_pair']:
                config = {
                    'output_dir': reporter_config.get('output_dir', './data'),
                    'batch_size': reporter_config.get('batch_size', 100)
                }
                reporter = AsyncFilePairReporter(config)
            else:
                # 默认使用AsyncFilePairReporter
                config = {
                    'output_dir': reporter_config.get('output_dir', './data'),
                    'batch_size': reporter_config.get('batch_size', 100)
                }
                reporter = AsyncFilePairReporter(config)

            if reporter:
                await reporter.initialize()
            return reporter

        except Exception as e:
            logger.error(f"创建异步报告器 '{reporter_type}' 失败: {e}", exc_info=True)
            return None

    @staticmethod
    async def create_reporters(reporter_config: Dict[str, Any], task_id: str) -> List[AsyncBaseReporter]:
        """根据配置创建多个异步报告器实例"""
        reporters = []

        # 假设配置格式与同步版本类似
        if 'active' in reporter_config and 'configurations' in reporter_config:
            active_reporters = reporter_config.get('active', [])
            configurations = reporter_config.get('configurations', {})

            for reporter_name in active_reporters:
                if reporter_name in configurations:
                    config = configurations[reporter_name]
                    reporter = await AsyncReporterFactory.create_reporter(config, task_id)
                    if reporter:
                        reporters.append(reporter)
                else:
                    logger.warning(f"[configurations]中找不到报告器配置: {reporter_name}")
        else:
            reporter = await AsyncReporterFactory.create_reporter(reporter_config, task_id)
            if reporter:
                reporters.append(reporter)

        if not reporters:
            default_reporter = await AsyncReporterFactory.create_reporter({'type': 'file_pair'}, task_id)
            if default_reporter:
                reporters.append(default_reporter)

        return reporters