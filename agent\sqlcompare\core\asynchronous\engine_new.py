# core/asynchronous/engine.py
import asyncio
import logging
import time
from typing import Optional

from connectors.asynchronous.base_connector import AsyncBaseConnector
from core.models import DiffResult, Record
from models.sqlalchemy_models import DiffStatus
from reporters.asynchronous.base_reporter import AsyncBaseReporter

logger = logging.getLogger(__name__)


def compare_values(value_a, value_b) -> bool:
    """Compare two values for equality, with optimizations."""
    if value_a is value_b:
        return True
    if value_a is None or value_b is None:
        return False
    try:
        return str(value_a) == str(value_b)
    except:
        return False


async def compare_sources(
    source_a: AsyncBaseConnector,
    source_b: AsyncBaseConnector,
    reporter: Async<PERSON>ase<PERSON>eporter,
):
    """Asynchronously compares two sorted data sources using a streaming merge algorithm."""
    logger.info("Starting asynchronous data comparison...")
    start_time = time.perf_counter()

    total_records_a = 0
    total_records_b = 0
    diff_count = 0

    try:
        async with source_a, source_b, reporter:
            iter_a = source_a.__aiter__()
            iter_b = source_b.__aiter__()

            record_a: Optional[Record] = await anext(iter_a, None)
            record_b: Optional[Record] = await anext(iter_b, None)

            while record_a is not None or record_b is not None:
                if record_a and (record_b is None or record_a.key < record_b.key):
                    total_records_a += 1
                    diff = DiffResult(
                        key=record_a.key,
                        value_a=record_a.value,
                        status=DiffStatus.SOURCE_ONLY,
                    )
                    await reporter.report_diff(diff, table_name)
                    diff_count += 1
                    record_a = await anext(iter_a, None)
                elif record_b and (record_a is None or record_b.key < record_a.key):
                    total_records_b += 1
                    diff = DiffResult(
                        key=record_b.key,
                        value_b=record_b.value,
                        status=DiffStatus.TARGET_ONLY,
                    )
                    await reporter.report_diff(diff, table_name)
                    diff_count += 1
                    record_b = await anext(iter_b, None)
                elif record_a and record_b:  # Keys are equal
                    total_records_a += 1
                    total_records_b += 1
                    if not compare_values(record_a.value, record_b.value):
                        diff = DiffResult(
                            key=record_a.key,
                            value_a=record_a.value,
                            value_b=record_b.value,
                            status=DiffStatus.DIFFERENT,
                        )
                        await reporter.report_diff(diff, table_name)
                        diff_count += 1
                    record_a = await anext(iter_a, None)
                    record_b = await anext(iter_b, None)

            await reporter.flush(table_name)

    except Exception as e:
        logger.error(f"An error occurred during async comparison: {e}", exc_info=True)
        raise
    finally:
        end_time = time.perf_counter()
        total_time = end_time - start_time
        logger.info(f"Asynchronous comparison finished in {total_time:.2f} seconds.")
        logger.info(f"Source A: {total_records_a} records, Source B: {total_records_b} records, Diffs: {diff_count}")


async def compare_sources_memory_dict(
    source_a: AsyncBaseConnector,
    source_b: AsyncBaseConnector,
    reporter: AsyncBaseReporter,
):
    """Compares two data sources by loading them into memory dictionaries concurrently."""
    logger.info("Starting async comparison (memory dictionary mode)...")
    start_time = time.perf_counter()

    try:
        async with source_a, source_b, reporter:
            logger.info("Concurrently loading data sources into memory...")
            task_a = asyncio.create_task(source_a.fetch_all_as_dict_async())
            task_b = asyncio.create_task(source_b.fetch_all_as_dict_async())

            data_a, data_b = await asyncio.gather(task_a, task_b)

            total_records_a = len(data_a)
            total_records_b = len(data_b)
            logger.info(f"Data loading complete. A: {total_records_a}, B: {total_records_b}")

            all_keys = data_a.keys() | data_b.keys()
            logger.info(f"Total unique keys: {len(all_keys)}. Comparing...")

            diff_count = 0
            for key in all_keys:
                value_a = data_a.get(key)
                value_b = data_b.get(key)

                diff = None
                if value_a is None:
                    diff = DiffResult(key=key, value_b=value_b, status=DiffStatus.TARGET_ONLY)
                elif value_b is None:
                    diff = DiffResult(key=key, value_a=value_a, status=DiffStatus.SOURCE_ONLY)
                elif not compare_values(value_a, value_b):
                    diff = DiffResult(
                        key=key,
                        value_a=value_a,
                        value_b=value_b,
                        status=DiffStatus.DIFFERENT,
                    )

                if diff:
                    await reporter.report_diff(diff, table_name)
                    diff_count += 1

            await reporter.flush(table_name)
            logger.info(f"Comparison complete. Found {diff_count} differences.")

    except Exception as e:
        logger.error(f"An error occurred during memory dictionary comparison: {e}", exc_info=True)
        raise
    finally:
        end_time = time.perf_counter()
        total_time = end_time - start_time
        logger.info(f"Memory dictionary comparison finished in {total_time:.2f} seconds.")
