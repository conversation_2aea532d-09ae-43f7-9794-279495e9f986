#!/usr/bin/env python3
"""
优化的比对结果数据模型
针对高性能写入和查询进行优化
"""
from sqlalchemy import Column, String, Text, DateTime, BigInteger, Index, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from datetime import datetime
from typing import Dict, Any, List, Optional
import logging

logger = logging.getLogger(__name__)

Base = declarative_base()


class OptimizedComparisonResult(Base):
    """
    优化的比对结果模型
    
    优化特点：
    1. 使用BigInteger主键支持大数据量
    2. 优化字段类型和长度
    3. 添加复合索引提升查询性能
    4. 支持分区表结构
    """
    __tablename__ = 'comparison_results_optimized'
    
    # 主键使用BigInteger支持大数据量
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    
    # 任务标识 - 使用VARCHAR(255)，添加索引
    task_id = Column(String(255), nullable=False, index=True)
    
    # 表名 - 使用VARCHAR(255)，添加索引
    table_name = Column(String(255), nullable=False, index=True)
    
    # 记录键 - 使用TEXT支持长键值
    record_key = Column(Text, nullable=False)
    
    # 差异状态 - 使用VARCHAR(50)，添加索引
    status = Column(String(50), nullable=False, index=True)
    
    # 字段名 - 使用VARCHAR(255)，可为空
    field_name = Column(String(255), nullable=True)
    
    # 源值 - 使用TEXT支持大文本
    source_value = Column(Text, nullable=True)
    
    # 目标值 - 使用TEXT支持大文本
    target_value = Column(Text, nullable=True)
    
    # 创建时间 - 使用服务器时间戳
    created_at = Column(DateTime, nullable=False, server_default=func.now())
    
    # 复合索引定义
    __table_args__ = (
        # 任务+表名复合索引（最常用的查询组合）
        Index('idx_task_table', 'task_id', 'table_name'),
        
        # 任务+状态复合索引（用于统计查询）
        Index('idx_task_status', 'task_id', 'status'),
        
        # 任务+表名+状态复合索引（用于详细查询）
        Index('idx_task_table_status', 'task_id', 'table_name', 'status'),
        
        # 覆盖索引（包含常用字段，避免回表查询）
        Index('idx_task_table_covering', 'task_id', 'table_name', 'status', 'record_key'),
        
        # 时间范围查询索引
        Index('idx_created_at', 'created_at'),
        
        # 任务+时间复合索引（用于按时间范围查询任务数据）
        Index('idx_task_time', 'task_id', 'created_at'),
    )
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'id': self.id,
            'task_id': self.task_id,
            'table_name': self.table_name,
            'record_key': self.record_key,
            'status': self.status,
            'field_name': self.field_name,
            'source_value': self.source_value,
            'target_value': self.target_value,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'OptimizedComparisonResult':
        """从字典创建实例"""
        return cls(
            task_id=data.get('task_id'),
            table_name=data.get('table_name'),
            record_key=data.get('record_key'),
            status=data.get('status'),
            field_name=data.get('field_name'),
            source_value=data.get('source_value'),
            target_value=data.get('target_value')
        )


class PartitionedComparisonResult(Base):
    """
    分区表版本的比对结果模型
    适用于超大数据量场景（千万级以上记录）
    """
    __tablename__ = 'comparison_results_partitioned'
    
    # 主键
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    
    # 分区键 - 使用task_id进行哈希分区
    task_id = Column(String(255), nullable=False, index=True)
    
    # 其他字段与优化模型相同
    table_name = Column(String(255), nullable=False, index=True)
    record_key = Column(Text, nullable=False)
    status = Column(String(50), nullable=False, index=True)
    field_name = Column(String(255), nullable=True)
    source_value = Column(Text, nullable=True)
    target_value = Column(Text, nullable=True)
    created_at = Column(DateTime, nullable=False, server_default=func.now())
    
    # 分区表的索引策略
    __table_args__ = (
        # 每个分区内的索引
        Index('idx_part_task_table', 'task_id', 'table_name'),
        Index('idx_part_task_status', 'task_id', 'status'),
        Index('idx_part_created_at', 'created_at'),
        
        # 分区表配置（通过SQL DDL实现）
        {
            'postgresql_partition_by': 'HASH (task_id)',
            'postgresql_with_oids': False
        }
    )


class ComparisonResultSummary(Base):
    """
    比对结果汇总表
    用于快速统计和报告生成
    """
    __tablename__ = 'comparison_result_summary'
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    task_id = Column(String(255), nullable=False, unique=True, index=True)
    table_name = Column(String(255), nullable=False, index=True)
    
    # 统计字段
    total_records = Column(BigInteger, nullable=False, default=0)
    source_only_count = Column(BigInteger, nullable=False, default=0)
    target_only_count = Column(BigInteger, nullable=False, default=0)
    different_count = Column(BigInteger, nullable=False, default=0)
    identical_count = Column(BigInteger, nullable=False, default=0)
    
    # 时间字段
    start_time = Column(DateTime, nullable=True)
    end_time = Column(DateTime, nullable=True)
    duration_seconds = Column(BigInteger, nullable=True)
    
    # 创建和更新时间
    created_at = Column(DateTime, nullable=False, server_default=func.now())
    updated_at = Column(DateTime, nullable=False, server_default=func.now(), onupdate=func.now())
    
    __table_args__ = (
        Index('idx_summary_task', 'task_id'),
        Index('idx_summary_table', 'table_name'),
        Index('idx_summary_created', 'created_at'),
    )


def get_optimized_table_ddl() -> str:
    """
    获取优化表结构的DDL语句
    包含所有性能优化配置
    """
    
    ddl = """
    -- 创建优化的比对结果表
    CREATE TABLE IF NOT EXISTS comparison_results_optimized (
        id BIGSERIAL PRIMARY KEY,
        task_id VARCHAR(255) NOT NULL,
        table_name VARCHAR(255) NOT NULL,
        record_key TEXT NOT NULL,
        status VARCHAR(50) NOT NULL,
        field_name VARCHAR(255),
        source_value TEXT,
        target_value TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    
    -- 创建高性能索引
    CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_opt_task_id ON comparison_results_optimized(task_id);
    CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_opt_table_name ON comparison_results_optimized(table_name);
    CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_opt_status ON comparison_results_optimized(status);
    CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_opt_task_table ON comparison_results_optimized(task_id, table_name);
    CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_opt_task_status ON comparison_results_optimized(task_id, status);
    CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_opt_task_table_status ON comparison_results_optimized(task_id, table_name, status);
    CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_opt_created_at ON comparison_results_optimized(created_at);
    CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_opt_task_time ON comparison_results_optimized(task_id, created_at);
    
    -- 覆盖索引（避免回表查询）
    CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_opt_covering 
    ON comparison_results_optimized(task_id, table_name, status) 
    INCLUDE (record_key, field_name);
    
    -- 创建分区表（适用于超大数据量）
    CREATE TABLE IF NOT EXISTS comparison_results_partitioned (
        id BIGSERIAL,
        task_id VARCHAR(255) NOT NULL,
        table_name VARCHAR(255) NOT NULL,
        record_key TEXT NOT NULL,
        status VARCHAR(50) NOT NULL,
        field_name VARCHAR(255),
        source_value TEXT,
        target_value TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) PARTITION BY HASH (task_id);
    
    -- 创建分区（4个分区）
    CREATE TABLE IF NOT EXISTS comparison_results_part_0 PARTITION OF comparison_results_partitioned
    FOR VALUES WITH (modulus 4, remainder 0);
    
    CREATE TABLE IF NOT EXISTS comparison_results_part_1 PARTITION OF comparison_results_partitioned
    FOR VALUES WITH (modulus 4, remainder 1);
    
    CREATE TABLE IF NOT EXISTS comparison_results_part_2 PARTITION OF comparison_results_partitioned
    FOR VALUES WITH (modulus 4, remainder 2);
    
    CREATE TABLE IF NOT EXISTS comparison_results_part_3 PARTITION OF comparison_results_partitioned
    FOR VALUES WITH (modulus 4, remainder 3);
    
    -- 为每个分区创建索引
    CREATE INDEX IF NOT EXISTS idx_part_0_task_table ON comparison_results_part_0(task_id, table_name);
    CREATE INDEX IF NOT EXISTS idx_part_1_task_table ON comparison_results_part_1(task_id, table_name);
    CREATE INDEX IF NOT EXISTS idx_part_2_task_table ON comparison_results_part_2(task_id, table_name);
    CREATE INDEX IF NOT EXISTS idx_part_3_task_table ON comparison_results_part_3(task_id, table_name);
    
    -- 创建汇总表
    CREATE TABLE IF NOT EXISTS comparison_result_summary (
        id BIGSERIAL PRIMARY KEY,
        task_id VARCHAR(255) NOT NULL UNIQUE,
        table_name VARCHAR(255) NOT NULL,
        total_records BIGINT DEFAULT 0,
        source_only_count BIGINT DEFAULT 0,
        target_only_count BIGINT DEFAULT 0,
        different_count BIGINT DEFAULT 0,
        identical_count BIGINT DEFAULT 0,
        start_time TIMESTAMP,
        end_time TIMESTAMP,
        duration_seconds BIGINT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    
    CREATE INDEX IF NOT EXISTS idx_summary_task ON comparison_result_summary(task_id);
    CREATE INDEX IF NOT EXISTS idx_summary_table ON comparison_result_summary(table_name);
    CREATE INDEX IF NOT EXISTS idx_summary_created ON comparison_result_summary(created_at);
    
    -- 创建更新触发器
    CREATE OR REPLACE FUNCTION update_updated_at_column()
    RETURNS TRIGGER AS $$
    BEGIN
        NEW.updated_at = CURRENT_TIMESTAMP;
        RETURN NEW;
    END;
    $$ language 'plpgsql';
    
    DROP TRIGGER IF EXISTS update_summary_updated_at ON comparison_result_summary;
    CREATE TRIGGER update_summary_updated_at
        BEFORE UPDATE ON comparison_result_summary
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
    """
    
    return ddl


def get_performance_tuning_sql() -> str:
    """
    获取PostgreSQL性能调优SQL
    专门针对比对结果存储优化
    """
    
    tuning_sql = """
    -- PostgreSQL性能调优配置
    
    -- 1. 内存配置优化
    ALTER SYSTEM SET shared_buffers = '256MB';
    ALTER SYSTEM SET effective_cache_size = '1GB';
    ALTER SYSTEM SET work_mem = '32MB';
    ALTER SYSTEM SET maintenance_work_mem = '512MB';
    
    -- 2. 写入性能优化
    ALTER SYSTEM SET wal_buffers = '32MB';
    ALTER SYSTEM SET checkpoint_completion_target = 0.9;
    ALTER SYSTEM SET max_wal_size = '2GB';
    ALTER SYSTEM SET min_wal_size = '512MB';
    
    -- 3. 连接和并发优化
    ALTER SYSTEM SET max_connections = 200;
    ALTER SYSTEM SET max_worker_processes = 8;
    ALTER SYSTEM SET max_parallel_workers = 4;
    ALTER SYSTEM SET max_parallel_workers_per_gather = 2;
    
    -- 4. 查询优化
    ALTER SYSTEM SET random_page_cost = 1.1;  -- SSD优化
    ALTER SYSTEM SET seq_page_cost = 1.0;
    ALTER SYSTEM SET cpu_tuple_cost = 0.01;
    ALTER SYSTEM SET cpu_index_tuple_cost = 0.005;
    
    -- 5. 自动清理优化
    ALTER SYSTEM SET autovacuum_max_workers = 4;
    ALTER SYSTEM SET autovacuum_naptime = '30s';
    ALTER SYSTEM SET autovacuum_vacuum_scale_factor = 0.1;
    ALTER SYSTEM SET autovacuum_analyze_scale_factor = 0.05;
    
    -- 重新加载配置
    SELECT pg_reload_conf();
    
    -- 显示当前配置
    SELECT name, setting, unit, context 
    FROM pg_settings 
    WHERE name IN (
        'shared_buffers', 'effective_cache_size', 'work_mem', 
        'maintenance_work_mem', 'wal_buffers', 'max_connections'
    )
    ORDER BY name;
    """
    
    return tuning_sql
