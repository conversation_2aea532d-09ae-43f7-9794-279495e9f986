# reporters/csvfile_reporter.py
import os
from typing import Dict, Any
from reporters.base_reporter import BaseReporter
from core.models import DiffResult

class CsvPairReporter(BaseReporter):
    """
    将差异报告写入成对的文本文件，以便于使用 Beyond Compare 等工具进行比较。
    为每个表生成两个文件：_sql_1.txt (源A) 和 _sql_2.txt (源B)。
    """

    def __init__(self, config: Dict[str, Any]):
        """
        初始化报告器。
        Args:
            config: 配置字典，可以包含 'data_dir'。
        """
        self.data_dir = config.get('data_dir', './data')
        self.file_handles: Dict[str, tuple] = {}
        print(f"CsvPairReporter 初始化，数据目录: {self.data_dir}")

    def open(self):
        """确保数据目录存在。"""
        try:
            if not os.path.exists(self.data_dir):
                os.makedirs(self.data_dir)
            print(f"报告目录已准备好: {self.data_dir}")
        except IOError as e:
            print(f"无法创建报告目录 {self.data_dir}: {e}")
            raise

    def close(self):
        """关闭所有打开的文件句柄。"""
        print("正在关闭 CsvPairReporter...")
        for table_name, (handle_a, handle_b) in self.file_handles.items():
            if handle_a:
                handle_a.close()
            if handle_b:
                handle_b.close()
            print(f"报告文件已关闭: {table_name}")
        self.file_handles.clear()

    def report_diff(self, diff_result: DiffResult, table_name: str):
        """
        将一条差异记录写入对应的两个文件中。
        """
        try:
            if table_name not in self.file_handles:
                self._open_files_for_table(table_name)

            handle_a, handle_b = self.file_handles[table_name]

            value_a_str = self._format_value(diff_result.value_a)
            value_b_str = self._format_value(diff_result.value_b)

            # 写入源A
            row_str_a = f"[{diff_result.key}]{value_a_str}\n"
            handle_a.write(row_str_a)

            # 写入源B
            row_str_b = f"[{diff_result.key}]{value_b_str}\n"
            handle_b.write(row_str_b)

        except IOError as e:
            print(f"写入报告文件时出错 (表: {table_name}): {e}")
            raise

    def _open_files_for_table(self, table_name: str):
        """为指定的表打开一对文件句柄。"""
        file_path_a = os.path.join(self.data_dir, f"{table_name}_sql_1.txt")
        file_path_b = os.path.join(self.data_dir, f"{table_name}_sql_2.txt")

        try:
            handle_a = open(file_path_a, "w", encoding="utf-8")
            handle_b = open(file_path_b, "w", encoding="utf-8")
            self.file_handles[table_name] = (handle_a, handle_b)
            print(f"为表 '{table_name}' 创建报告文件:\n  - {file_path_a}\n  - {file_path_b}")
        except IOError as e:
            print(f"无法为表 {table_name} 创建报告文件: {e}")
            raise
    
    def _format_value(self, value) -> str:
        """格式化值为字符串，处理None值。"""
        if value is None:
            return ""
        return str(value)