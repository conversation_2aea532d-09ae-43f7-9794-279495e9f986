#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
结果查询API端点 - 使用主项目模型和服务
"""

import os
import sys
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from pathlib import Path

# 确保能导入SQLCompare核心模块
project_root = Path(__file__).parent.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

# 使用主项目的模型和服务
from models.pydantic_models import ComparisonResultResponse, TaskStatistics
from models.sqlalchemy_models import DiffType, DiffStatus
from backend.models.base import APIResponse, PaginationParams
from backend.core.dependencies import get_comparison_service

router = APIRouter()


# 比对结果查询
@router.get("/{task_id}", response_model=APIResponse)
async def get_comparison_results(
    task_id: str,
    limit: int = Query(100, description="返回结果数量限制"),
    offset: int = Query(0, description="结果偏移量"),
    comparison_service: ComparisonService = Depends(get_comparison_service)
):
    """
    获取任务的比对结果
    
    - **task_id**: 任务ID
    - **limit**: 返回结果数量限制
    - **offset**: 结果偏移量
    """
    try:
        results = comparison_service.get_comparison_results(task_id, limit, offset)
        return APIResponse.success_response(data={
            "results": results,
            "limit": limit,
            "offset": offset,
            "total": len(results)
        })
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{task_id}/summary", response_model=APIResponse)
async def get_result_summary(
    task_id: str,
    comparison_service: ComparisonService = Depends(get_comparison_service)
):
    """
    获取比对结果汇总
    
    - **task_id**: 任务ID
    """
    try:
        # 获取任务汇总信息
        task_summary = comparison_service.get_task_summary(task_id)
        if not task_summary:
            raise HTTPException(status_code=404, detail=f"任务 {task_id} 不存在")
        
        # 获取比对结果统计
        results = comparison_service.get_comparison_results(task_id, limit=1000)
        
        # 计算统计信息
        total_records = len(results)
        different_records = len([r for r in results if r.get('difference_type') == 'DIFFERENT'])
        missing_records = len([r for r in results if r.get('difference_type') == 'MISSING'])
        extra_records = len([r for r in results if r.get('difference_type') == 'EXTRA'])
        
        summary = {
            "task_id": task_id,
            "task_name": task_summary.get("task_name", ""),
            "status": task_summary.get("status", ""),
            "total_records": total_records,
            "different_records": different_records,
            "missing_records": missing_records,
            "extra_records": extra_records,
            "match_rate": (total_records - different_records - missing_records - extra_records) / max(total_records, 1) * 100
        }
        
        return APIResponse.success_response(data=summary)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{task_id}/statistics", response_model=APIResponse)
async def get_result_statistics(
    task_id: str,
    comparison_service: ComparisonService = Depends(get_comparison_service)
):
    """
    获取比对结果统计信息
    
    - **task_id**: 任务ID
    """
    try:
        # 获取比对结果
        results = comparison_service.get_comparison_results(task_id, limit=10000)
        
        # 按表名分组统计
        table_stats = {}
        for result in results:
            table_name = result.get('table_name', 'unknown')
            if table_name not in table_stats:
                table_stats[table_name] = {
                    "table_name": table_name,
                    "total": 0,
                    "different": 0,
                    "missing": 0,
                    "extra": 0
                }
            
            table_stats[table_name]["total"] += 1
            diff_type = result.get('difference_type', '')
            if diff_type == 'DIFFERENT':
                table_stats[table_name]["different"] += 1
            elif diff_type == 'MISSING':
                table_stats[table_name]["missing"] += 1
            elif diff_type == 'EXTRA':
                table_stats[table_name]["extra"] += 1
        
        statistics = {
            "task_id": task_id,
            "total_tables": len(table_stats),
            "total_records": len(results),
            "table_statistics": list(table_stats.values())
        }
        
        return APIResponse.success_response(data=statistics)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
