#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
单表比对结果页面组件
"""
import os
import sys
import json
import logging
import threading
import tkinter as tk
from pathlib import Path
from tkinter import ttk, messagebox
sys.path.append(str(Path(__file__).parent))
sys.path.append(str(Path(__file__).parent.parent))
from base_page import BasePage
from utils.sqlite_data_manager import SqliteDataManager

logger = logging.getLogger(__name__)

class TableComparisonPage(BasePage):
    """单表比对结果页面"""
    
    def __init__(self, parent, app_instance=None, table_name=""):
        self.table_name = table_name
        self.comparison_data = []
        # 分页相关属性
        self.current_page = 1
        self.page_size = 100
        self.total_records = 0
        self.total_pages = 0

        # 配置管理器和比对相关
        self.config_manager = None
        self.comparison_running = False
        self.comparison_results = []

        # SQLite数据管理器
        self.sqlite_manager = None
        self.use_sqlite_data = False  # 是否使用SQLite数据

        # 搜索和过滤
        self.current_search_term = ""
        self.current_status_filter = ""

        super().__init__(parent, app_instance)

        # 初始化配置管理器和数据管理器
        self._init_config_manager()
        self._init_sqlite_manager()

    def destroy(self):
        """销毁页面时清理资源"""
        try:
            # 关闭SQLite连接
            if self.sqlite_manager and self.sqlite_manager.is_connected():
                self.sqlite_manager.disconnect()
                logger.info("已关闭SQLite数据管理器连接")
        except Exception as e:
            logger.warning(f"关闭SQLite连接时出错: {e}")

        # 调用父类的销毁方法
        super().destroy()
    
    def _create_ui(self):
        """创建比对结果界面 - 极简版，表格填满工作区域"""
        self.frame = ttk.Frame(self.parent)

        # 结果表格区域
        self._create_results_table_section(self.frame)

        # 分页控件区域
        self._create_pagination_section(self.frame)

        # 尝试加载数据
        self._load_table_data()    

    def _create_results_table_section(self, parent):
        """创建结果表格区域 - 保持原有容器样式，表格使用扁平化设计"""
        # 直接创建表格容器，保持原有样式
        table_container = ttk.Frame(parent)
        table_container.pack(fill=tk.BOTH, expand=True, pady=(0, 5))

        # 定义表格列
        columns = ("记录ID", "差异类型", "主键值", "源数据", "目标数据")
        # 创建表格，使用扁平化样式去除凸起感
        self.results_tree = ttk.Treeview(table_container, columns=columns, show="headings")

        # 配置表格样式 - 扁平化设计
        style = ttk.Style()
        style.configure("Flat.Treeview", relief="flat", borderwidth=1)
        self.results_tree.configure(style="Flat.Treeview")

        # 设置列标题和宽度
        column_widths = {"记录ID": 30, "差异类型": 60, "主键值": 100, "源数据": 300, "目标数据": 300}
        for col in columns:
            self.results_tree.heading(col, text=col, command=lambda c=col: self._sort_by_column(c))
            self.results_tree.column(col, width=column_widths.get(col, 120), anchor="center")

        # 添加智能滚动条 - 只在需要时显示
        self.v_scrollbar = ttk.Scrollbar(table_container, orient="vertical", command=self.results_tree.yview)
        self.h_scrollbar = ttk.Scrollbar(table_container, orient="horizontal", command=self.results_tree.xview)
        self.results_tree.configure(yscrollcommand=self._on_v_scroll, xscrollcommand=self._on_h_scroll)

        # 布局 - 表格先占满空间
        self.results_tree.grid(row=0, column=0, sticky="nsew")
        # 滚动条初始隐藏，根据需要动态显示

        table_container.grid_rowconfigure(0, weight=1)
        table_container.grid_columnconfigure(0, weight=1)

        # 绑定事件以动态调整滚动条显示
        self.results_tree.bind('<Configure>', self._on_tree_configure)

        # 配置行颜色 - 包含隔行背景色
        if 0:
            self.results_tree.tag_configure("matched", background="#e8f5e8")
            self.results_tree.tag_configure("different", background="#ffe8e8")
            self.results_tree.tag_configure("source_only", background="#fff8dc")
            self.results_tree.tag_configure("target_only", background="#f0e8ff")

        # 配置隔行背景色
        self.results_tree.tag_configure("even_row", background="#e0e8ef")  # 浅蓝色
        self.results_tree.tag_configure("odd_row", background="white")     # 白色

    def _create_pagination_section(self, parent):
        """创建分页控件区域 - 紧凑布局，居中靠左对齐"""
        # 创建分页控件容器，使用较小的上下间距（约2mm）
        pagination_container = ttk.Frame(parent)
        pagination_container.pack(fill=tk.X, pady=(6, 6))

        # 创建居中靠左的分页控件框架
        pagination_frame = ttk.Frame(pagination_container)
        pagination_frame.pack(expand=True)

        # 左侧：记录信息
        info_frame = ttk.Frame(pagination_frame)
        info_frame.pack(side=tk.LEFT, padx=(0, 30))

        self.record_info_label = ttk.Label(info_frame, text="共 0 条记录")
        self.record_info_label.pack(side=tk.LEFT)

        # 中间：分页控件
        nav_frame = ttk.Frame(pagination_frame)
        nav_frame.pack(side=tk.LEFT, padx=(0, 30))

        # 首页按钮
        self.first_btn = ttk.Button(nav_frame, text="首页", width=6,
                                   command=self._go_to_first_page)
        self.first_btn.pack(side=tk.LEFT, padx=(0, 2))

        # 上一页按钮
        self.prev_btn = ttk.Button(nav_frame, text="上一页", width=6,
                                  command=self._go_to_prev_page)
        self.prev_btn.pack(side=tk.LEFT, padx=(0, 2))

        # 页码输入
        ttk.Label(nav_frame, text="第").pack(side=tk.LEFT, padx=(10, 2))

        self.page_var = tk.StringVar(value="1")
        self.page_entry = ttk.Entry(nav_frame, textvariable=self.page_var, width=5)
        self.page_entry.pack(side=tk.LEFT, padx=(0, 2))
        self.page_entry.bind("<Return>", self._on_page_entry_changed)

        self.total_pages_label = ttk.Label(nav_frame, text="页 / 共 0 页")
        self.total_pages_label.pack(side=tk.LEFT, padx=(2, 10))

        # 下一页按钮
        self.next_btn = ttk.Button(nav_frame, text="下一页", width=6,
                                  command=self._go_to_next_page)
        self.next_btn.pack(side=tk.LEFT, padx=(0, 2))

        # 末页按钮
        self.last_btn = ttk.Button(nav_frame, text="末页", width=6,
                                  command=self._go_to_last_page)
        self.last_btn.pack(side=tk.LEFT, padx=(0, 2))

        # 右侧：每页显示数量
        size_frame = ttk.Frame(pagination_frame)
        size_frame.pack(side=tk.LEFT)

        ttk.Label(size_frame, text="每页显示:").pack(side=tk.LEFT, padx=(0, 5))

        self.page_size_var = tk.StringVar(value="100")
        page_size_combo = ttk.Combobox(size_frame, textvariable=self.page_size_var,
                                      values=["50", "100", "200", "500"], width=8,
                                      state="readonly")
        page_size_combo.pack(side=tk.LEFT, padx=(0, 5))
        page_size_combo.bind("<<ComboboxSelected>>", self._on_page_size_changed)

        ttk.Label(size_frame, text="条").pack(side=tk.LEFT)
    
    def _init_config_manager(self):
        """初始化配置管理器"""
        if not self.config_manager.config:
            config_files = self.config_manager.auto_discover_configs()
            if config_files:
                self.config_manager.load_config(config_files[0])
            else:
                self.app._log_message("未找到任何配置文件。", "WARNING")

    def _init_sqlite_manager(self):
        """初始化SQLite数据管理器"""
        if SqliteDataManager is None:
            logger.warning("SQLite数据管理器不可用")
            return

        try:
            self.sqlite_manager = SqliteDataManager()
            if self.sqlite_manager.connect():
                # 检查是否有当前表的数据
                available_tables = self.sqlite_manager.get_available_tables()
                if self.table_name in available_tables:
                    self.use_sqlite_data = True
                    logger.info(f"找到表 {self.table_name} 的SQLite数据")
                else:
                    logger.info(f"未找到表 {self.table_name} 的SQLite数据，将使用模拟数据")
            else:
                logger.warning("无法连接到SQLite数据库")
        except Exception as e:
            logger.error(f"初始化SQLite管理器失败: {e}")
            self.sqlite_manager = None

    def _load_table_data(self):
        """加载比对数据 - 优先从SQLite加载真实数据"""
        try:
            # 优先尝试从SQLite加载真实数据
            if self.use_sqlite_data and self.sqlite_manager:
                self._load_sqlite_data()
                return

            # 如果没有SQLite数据，尝试从配置执行比对
            if not self.config_manager:
                self._load_sample_data()  # 改为加载示例数据
                return

            # 检查是否有表配置
            tables = self.config_manager.get_comparison_tables()
            if not tables:
                self._load_sample_data()  # 改为加载示例数据
                return

            # 如果指定了表名，查找对应的表配置
            target_table = None
            if self.table_name:
                for table in tables:
                    if table.get('table_id') == self.table_name:
                        target_table = table
                        break
            else:
                # 如果没有指定表名，使用第一个表
                target_table = tables[0] if tables else None

            if not target_table:
                # 如果没有找到指定表，使用第一个表的配置进行演示
                if tables:
                    target_table = tables[0]
                    logger.info(f"🔄 未找到表 '{self.table_name}'，使用 '{target_table.get('table_id')}' 进行演示")
                else:
                    logger.warning(f"⚠️ 未找到表 '{self.table_name}' 的配置")
                    self._load_sample_data()  # 改为加载示例数据
                    return

            # 启动比对任务
            self._start_comparison_task(target_table)

        except Exception as e:
            logger.error(f"❌ 加载数据失败: {e}")
            self._load_empty_data()

    def _load_empty_data(self):
        """加载空数据状态"""
        self.comparison_data = []
        self.total_records = 0

        # 安全获取页面大小
        try:
            if hasattr(self, 'page_size_var') and self.page_size_var:
                self.page_size = int(self.page_size_var.get())
            else:
                self.page_size = 100  # 默认值
        except (AttributeError, ValueError):
            self.page_size = 100

        self.total_pages = 0
        self.current_page = 1

        # 更新分页信息并显示空数据
        self._update_pagination_info()
        self._load_current_page_data()

    def _load_sqlite_data(self):
        """从SQLite数据库加载真实比对数据"""
        try:
            if not self.sqlite_manager or not self.sqlite_manager.is_connected():
                logger.warning("SQLite管理器未连接")
                self._load_sample_data()
                return

            # 获取表统计信息
            stats = self.sqlite_manager.get_table_statistics(self.table_name)
            self.total_records = stats.get('total', 0)

            if self.total_records == 0:
                logger.info(f"表 {self.table_name} 没有差异数据")
                self._load_empty_data()
                return

            # 计算总页数
            self.total_pages = max(1, (self.total_records + self.page_size - 1) // self.page_size)

            # 加载当前页数据
            self._load_current_page_sqlite_data()

            # 更新分页信息
            self._update_pagination_info()

            logger.info(f"✅ 从SQLite加载表 {self.table_name} 数据成功，总记录数: {self.total_records}")

        except Exception as e:
            logger.error(f"从SQLite加载数据失败: {e}")
            self._load_sample_data()

    def _load_current_page_sqlite_data(self):
        """从SQLite加载当前页数据"""
        try:
            if not self.sqlite_manager:
                return

            # 获取分页数据
            data, total = self.sqlite_manager.get_comparison_data(
                table_name=self.table_name,
                page=self.current_page,
                page_size=self.page_size,
                status_filter=self.current_status_filter if self.current_status_filter else None,
                search_term=self.current_search_term if self.current_search_term else None
            )

            # 转换为表格显示格式
            self.comparison_data = []
            for item in data:
                # 构建显示数据
                display_item = {
                    'record_id': str(item['id']),
                    'primary_key': item['record_key'],
                    'diff_type': self._get_diff_type_display(item['status']),
                    'field_name': item['field_name'] or '整行',
                    'source_data': item['value_a'] or '',
                    'target_data': item['value_b'] or '',
                    'status': item['status'],
                    'created_at': item['created_at']
                }
                self.comparison_data.append(display_item)

            # 更新总记录数（可能因为搜索/过滤而变化）
            if self.current_search_term or self.current_status_filter:
                self.total_records = total
                self.total_pages = max(1, (self.total_records + self.page_size - 1) // self.page_size)

            # 加载到表格
            self._load_current_page_data()

        except Exception as e:
            logger.error(f"加载SQLite当前页数据失败: {e}")

    def _get_diff_type_display(self, status: str) -> str:
        """将状态转换为显示文本"""
        status_map = {
            'DF': '数据不一致',
            'SO': '仅在源中存在',
            'TO': '仅在目标中存在',
            'FD': '字段差异',
            'ID': '完全相同',
            # 兼容旧格式
            'DIFFERENT': '数据不一致',
            'IN_A_ONLY': '仅在源中存在',
            'IN_B_ONLY': '仅在目标中存在',
            'FIELD_DIFF': '字段差异'
        }
        return status_map.get(status, status)

    def _load_sample_data(self):
        """加载示例数据用于测试表格显示"""
        print("加载示例数据...")

        # 创建示例比较数据
        sample_data = [
            {
                "record_id": "001",
                "diff_type": "数据不一致",
                "primary_key": "shares_001",
                "source_data": {"id": "shares_001", "amount": "56.86", "email": "<EMAIL>", "name": "用户1"},
                "target_data": {"id": "shares_001", "amount": "34.56", "email": "<EMAIL>", "name": "用户1"},
                "diff_fields": ["amount"]
            },
            {
                "record_id": "002",
                "diff_type": "数据不一致",
                "primary_key": "shares_002",
                "source_data": {"id": "shares_002", "amount": "67.23", "email": "<EMAIL>", "name": "用户2"},
                "target_data": {"id": "shares_002", "amount": "67.23", "email": "<EMAIL>", "name": "用户2"},
                "diff_fields": ["email"]
            },
            {
                "record_id": "003",
                "diff_type": "仅在源中存在",
                "primary_key": "shares_003",
                "source_data": {"id": "shares_003", "amount": "NULL", "email": "<EMAIL>", "name": "用户3"},
                "target_data": None,
                "diff_fields": []
            },
            {
                "record_id": "004",
                "diff_type": "数据不一致",
                "primary_key": "shares_004",
                "source_data": {"id": "shares_004", "amount": "123.45", "email": "<EMAIL>", "name": "测试用户"},
                "target_data": {"id": "shares_004", "amount": "678.90", "email": "<EMAIL>", "name": "测试用户"},
                "diff_fields": ["amount", "email"]
            },
            {
                "record_id": "005",
                "diff_type": "仅在目标中存在",
                "primary_key": "shares_005",
                "source_data": None,
                "target_data": {"id": "shares_005", "amount": "999.99", "email": "<EMAIL>", "name": "新用户"},
                "diff_fields": []
            }
        ]

        # 更新数据
        self._update_comparison_data(sample_data)
        print(f"✅ 示例数据加载完成，共 {len(sample_data)} 条记录")

    def _start_comparison_task(self, table_config):
        """启动比对任务"""
        try:
            if self.comparison_running:
                logger.warning("⚠️ 比对任务正在运行中")
                return

            self.comparison_running = True
            logger.info(f"🚀 开始比对表: {table_config.get('table_id', '未知')}")

            # 在后台线程中执行比对
            def comparison_thread():
                try:
                    self._execute_comparison(table_config)
                except Exception as e:
                    logger.error(f"❌ 比对执行失败: {e}")
                    # 在主线程中显示错误
                    self.parent.after(0, lambda: self._on_comparison_error(str(e)))
                finally:
                    self.comparison_running = False

            thread = threading.Thread(target=comparison_thread, daemon=True)
            thread.start()

        except Exception as e:
            logger.error(f"❌ 启动比对任务失败: {e}")
            self.comparison_running = False

    def _execute_comparison(self, table_config):
        """执行比对逻辑"""
        try:
            logger.info(f"🔍 开始执行表 {table_config.get('table_id')} 的比对")

            # 获取数据库配置
            source_config = self.config_manager.get_database_config('DB1')
            target_config = self.config_manager.get_database_config('DB2')

            if not source_config or not target_config:
                raise Exception("数据库配置不完整")

            logger.info(f"📊 源数据库: {source_config.db_type} - {source_config.host}:{source_config.port}")
            logger.info(f"📊 目标数据库: {target_config.db_type} - {target_config.host}:{target_config.port}")
            logger.info(f"📝 源SQL: {table_config.get('sql_1', '')[:100]}...")
            logger.info(f"📝 目标SQL: {table_config.get('sql_2', '')[:100]}...")

            # 由于缺少完整的比对引擎，这里创建一些演示数据
            # 在实际环境中，这里应该调用真正的比对引擎
            differences = self._create_demo_differences(table_config)

            # 处理结果
            self._process_comparison_results(differences)

        except Exception as e:
            logger.error(f"❌ 比对执行失败: {e}")
            raise

    def _create_demo_differences(self, table_config=None):
        """创建演示差异数据（基于真实配置信息）"""
        import random

        differences = []

        # 获取真实的配置信息
        if table_config:
            table_id = table_config.get('table_id', 'UNKNOWN_TABLE')
            table_name = table_config.get('remark', table_id)
        else:
            table_id = self.table_name or 'user_table'
            table_name = '用户信息表'

        # 获取数据库配置信息用于演示
        source_db_info = "未知数据库"
        target_db_info = "未知数据库"

        if self.config_manager:
            try:
                source_config = self.config_manager.get_database_config('DB1')
                target_config = self.config_manager.get_database_config('DB2')

                if source_config:
                    source_db_info = f"{source_config.db_type.upper()} - {source_config.host}:{source_config.port}/{source_config.database}"
                if target_config:
                    target_db_info = f"{target_config.db_type.upper()} - {target_config.host}:{target_config.port}/{target_config.database}"

                logger.info(f"📊 源数据库: {source_db_info}")
                logger.info(f"📊 目标数据库: {target_db_info}")
            except Exception as e:
                logger.warning(f"⚠️ 获取数据库配置失败: {e}")

        # 创建一些演示差异（基于真实表结构）
        sample_fields = ['id', 'name', 'email', 'status', 'create_time', 'update_time', 'amount', 'category']

        for i in range(15):
            diff_type = random.choice(['IN_A_ONLY', 'IN_B_ONLY', 'DIFFERENT'])

            # 模拟差异对象
            class MockDiff:
                def __init__(self, status, key, value_a=None, value_b=None):
                    self.status = status
                    self.key = key
                    self.value_a = value_a
                    self.value_b = value_b

            # 生成更真实的数据
            record_id = f"{table_id}_{i+1:03d}"

            if diff_type == 'IN_A_ONLY':
                diff = MockDiff(
                    status='IN_A_ONLY',
                    key=record_id,
                    value_a={
                        'id': i+1,
                        'name': f'用户{i+1}',
                        'email': f'user{i+1}@example.com',
                        'status': random.choice(['active', 'inactive']),
                        'create_time': '2024-01-15 10:30:25'
                    },
                    value_b=None
                )
            elif diff_type == 'IN_B_ONLY':
                diff = MockDiff(
                    status='IN_B_ONLY',
                    key=record_id,
                    value_a=None,
                    value_b={
                        'id': i+1,
                        'name': f'新用户{i+1}',
                        'email': f'newuser{i+1}@example.com',
                        'status': 'pending',
                        'create_time': '2024-01-15 10:35:00'
                    }
                )
            else:  # DIFFERENT
                diff = MockDiff(
                    status='DIFFERENT',
                    key=record_id,
                    value_a={
                        'id': i+1,
                        'name': f'用户{i+1}',
                        'email': f'user{i+1}@example.com',
                        'status': 'active',
                        'amount': round(random.uniform(100, 1000), 2),
                        'update_time': '2024-01-15 10:30:25'
                    },
                    value_b={
                        'id': i+1,
                        'name': f'用户{i+1}',
                        'email': f'user{i+1}@newdomain.com',  # 差异：邮箱域名不同
                        'status': 'inactive',  # 差异：状态不同
                        'amount': round(random.uniform(100, 1000), 2),  # 差异：金额不同
                        'update_time': '2024-01-15 10:35:00'  # 差异：更新时间不同
                    }
                )

            differences.append(diff)

        logger.info(f"🎯 基于表 '{table_name}' 生成了 {len(differences)} 条演示差异数据")
        logger.info(f"📋 数据源: {source_db_info} ↔ {target_db_info}")
        return differences

    def _create_connector(self, db_config, sql_query):
        """根据数据库类型创建连接器（延迟导入）"""
        try:
            # 延迟导入连接器以避免DLL加载问题
            db_type = db_config.db_type.lower()

            if db_type == 'db2':
                from connectors.db2_connector import DB2Connector
                connector_class = DB2Connector
            elif db_type == 'mysql':
                from connectors.mysql_connector import MySqlConnector
                connector_class = MySqlConnector
            elif db_type == 'oracle':
                from connectors.oracle_connector import OracleConnector
                connector_class = OracleConnector
            elif db_type == 'gaussdb':
                from connectors.gaussdb_connector import GaussDBConnector
                connector_class = GaussDBConnector
            else:
                raise Exception(f"不支持的数据库类型: {db_type}")

            # 构建数据库配置字典
            db_config_dict = {
                'ip': db_config.host,
                'port': db_config.port,
                'user': db_config.username,
                'password': db_config.password,
                'database': db_config.database
            }

            # 创建连接器实例
            connector = connector_class(
                db_config=db_config_dict,
                query=sql_query
            )

            return connector

        except Exception as e:
            logger.error(f"❌ 创建连接器失败: {e}")
            raise

    def _process_comparison_results(self, differences):
        """处理比对结果"""
        try:
            # 转换差异结果为显示格式
            comparison_data = []
            for i, diff in enumerate(differences):
                record_id = f"{i+1:03d}"

                # 映射状态
                status_map = {
                    'SO': '仅在源中存在',
                    'TO': '仅在目标中存在',
                    'DF': '数据不匹配',
                    'FD': '字段差异',
                    'ID': '完全相同',
                    # 兼容旧格式
                    'IN_A_ONLY': '仅在源中存在',
                    'IN_B_ONLY': '仅在目标中存在',
                    'DIFFERENT': '数据不匹配'
                }

                diff_type = status_map.get(diff.status, '未知状态')
                primary_key = diff.key

                # 处理数据值
                source_data = diff.value_a if hasattr(diff, 'value_a') else None
                target_data = diff.value_b if hasattr(diff, 'value_b') else None

                comparison_data.append({
                    "record_id": record_id,
                    "diff_type": diff_type,
                    "primary_key": primary_key,
                    "source_data": source_data,
                    "target_data": target_data
                })

            # 在主线程中更新UI
            self.parent.after(0, lambda: self._update_comparison_data(comparison_data))

        except Exception as e:
            logger.error(f"❌ 处理比对结果失败: {e}")
            self.parent.after(0, lambda: self._on_comparison_error(str(e)))

    def _update_comparison_data(self, comparison_data):
        """在主线程中更新比对数据"""
        try:
            self.comparison_data = comparison_data
            self.total_records = len(self.comparison_data)

            # 安全获取页面大小
            try:
                if hasattr(self, 'page_size_var') and self.page_size_var:
                    self.page_size = int(self.page_size_var.get())
                else:
                    self.page_size = 100  # 默认值
            except (AttributeError, ValueError):
                self.page_size = 100

            self.total_pages = (self.total_records + self.page_size - 1) // self.page_size if self.total_records > 0 else 0
            self.current_page = 1

            # 更新分页信息并显示第一页数据
            self._update_pagination_info()
            self._load_current_page_data()

            logger.info(f"✅ 比对完成，发现 {self.total_records} 条差异")

        except Exception as e:
            logger.error(f"❌ 更新比对数据失败: {e}")

    def _on_comparison_error(self, error_message):
        """比对错误处理"""
        try:
            logger.error(f"❌ 比对失败: {error_message}")
            messagebox.showerror("比对错误", f"比对执行失败:\n{error_message}")
            self._load_empty_data()
        except Exception as e:
            logger.error(f"❌ 错误处理失败: {e}")

    def _load_current_page_data(self):
        """加载当前页数据"""
        # 清空表格
        if self.results_tree:
            for item in self.results_tree.get_children():
                self.results_tree.delete(item)
        elif self.results_table and hasattr(self.results_table, 'clear'):
            self.results_table.clear()

        # 计算当前页的数据范围
        start_index = (self.current_page - 1) * self.page_size
        end_index = min(start_index + self.page_size, self.total_records)

        # 获取当前页数据
        current_page_data = self.comparison_data[start_index:end_index]

        # 插入数据到表格
        for index, data in enumerate(current_page_data):
            record_id = data["record_id"]
            diff_type = data["diff_type"]
            primary_key = data["primary_key"]

            # 根据表格类型选择不同的处理方式
            if self.results_tree:
                # 使用原来的Treeview
                source_str = self._format_data_with_highlight(data["source_data"], data["target_data"], is_source=True)
                target_str = self._format_data_with_highlight(data["target_data"], data["source_data"], is_source=False)

                tags = []
                if index % 2 == 0:
                    tags.append("even_row")
                else:
                    tags.append("odd_row")

                if diff_type == "数据不匹配" or diff_type == "数据不一致":
                    tags.append("diff_row")
                elif diff_type == "仅在源中存在":
                    tags.append("diff_row")
                elif diff_type == "仅在目标中存在":
                    tags.append("diff_row")
                elif diff_type == "匹配":
                    tags.append("matched")

                values = (record_id, diff_type, primary_key, source_str, target_str)
                self.results_tree.insert("", tk.END, values=values, tags=tuple(tags))
            else:
                # 备用方案：如果没有results_tree，也使用相同的格式化逻辑
                source_str = self._format_data_with_highlight(data["source_data"], data["target_data"], is_source=True)
                target_str = self._format_data_with_highlight(data["target_data"], data["source_data"], is_source=False)

                tags = []
                if index % 2 == 0:
                    tags.append("even_row")
                else:
                    tags.append("odd_row")

                if diff_type == "数据不匹配" or diff_type == "数据不一致":
                    tags.append("diff_row")
                elif diff_type == "仅在源中存在":
                    tags.append("diff_row")
                elif diff_type == "仅在目标中存在":
                    tags.append("diff_row")
                elif diff_type == "匹配":
                    tags.append("matched")

                values = (record_id, diff_type, primary_key, source_str, target_str)

                # 如果有results_table且有insert_row方法，使用它
                if self.results_table and hasattr(self.results_table, 'insert_row'):
                    self.results_table.insert_row(values, tags, {})
                else:
                    # 否则输出到控制台作为调试信息
                    print(f"数据行: {values}")

    def _format_data(self, data):
        """格式化数据显示"""
        if data is None:
            return "NULL"
        if isinstance(data, dict):
            return json.dumps(data, ensure_ascii=False, separators=(',', ':'))
        return str(data)

    def _format_data_with_highlight(self, current_data, other_data, is_source=True):
        """格式化数据显示，支持差异高亮"""
        if current_data is None:
            return "NULL"

        if other_data is None:
            # 对方为空，当前数据完整显示
            return self._format_data(current_data)

        # 如果两个数据都是字典，进行字段级别的差异比较
        if isinstance(current_data, dict) and isinstance(other_data, dict):
            return self._format_dict_with_highlight(current_data, other_data)

        # 非字典类型，直接比较值
        current_str = str(current_data)
        other_str = str(other_data)

        if current_str != other_str:
            return f"【{current_str}】"  # 使用方括号更明显
        else:
            return current_str

    def _format_dict_with_highlight(self, current_dict, other_dict):
        """格式化字典数据，高亮显示差异字段的值"""
        if not isinstance(current_dict, dict) or not isinstance(other_dict, dict):
            return self._format_data(current_dict)

        result_parts = []

        for key, value in current_dict.items():
            current_value_str = str(value)
            other_value = other_dict.get(key)
            other_value_str = str(other_value) if other_value is not None else "NULL"

            if current_value_str != other_value_str:
                # 值不同，高亮显示
                result_parts.append(f"{key}:【{current_value_str}】")
            else:
                # 值相同，正常显示
                result_parts.append(f"{key}:{current_value_str}")

        return ", ".join(result_parts)

    def _on_item_double_click(self, event):
        """双击事件处理"""
        selection = self.results_tree.selection()
        if selection:
            item = selection[0]
            values = self.results_tree.item(item, "values")
            print(f"双击行: {values}")

    def _on_row_double_click(self, row_index, row_data):
        """处理行双击事件"""
        try:
            # 获取行数据
            values = row_data['values']
            if len(values) >= 5:
                record_id = values[0]
                diff_type = values[1]
                primary_key = values[2]
                source_data = values[3]
                target_data = values[4]

                # 显示详细信息（可以扩展为弹出详细对话框）
                print(f"双击行: {record_id}, 类型: {diff_type}")
                print(f"源数据: {source_data}")
                print(f"目标数据: {target_data}")

        except Exception as e:
            logger.error(f"处理双击事件失败: {e}")

    def _update_pagination_info(self):
        """更新分页信息显示"""
        # 更新记录信息
        self.record_info_label.config(text=f"共 {self.total_records} 条记录")

        # 更新页码信息
        self.page_var.set(str(self.current_page))
        self.total_pages_label.config(text=f"页 / 共 {self.total_pages} 页")

        # 更新按钮状态
        self.first_btn.config(state="normal" if self.current_page > 1 else "disabled")
        self.prev_btn.config(state="normal" if self.current_page > 1 else "disabled")
        self.next_btn.config(state="normal" if self.current_page < self.total_pages else "disabled")
        self.last_btn.config(state="normal" if self.current_page < self.total_pages else "disabled")

    def _go_to_first_page(self):
        """跳转到首页"""
        if self.current_page > 1:
            self.current_page = 1
            self._update_pagination_info()
            self._reload_current_page()

    def _go_to_prev_page(self):
        """跳转到上一页"""
        if self.current_page > 1:
            self.current_page -= 1
            self._update_pagination_info()
            self._reload_current_page()

    def _go_to_next_page(self):
        """跳转到下一页"""
        if self.current_page < self.total_pages:
            self.current_page += 1
            self._update_pagination_info()
            self._reload_current_page()

    def _go_to_last_page(self):
        """跳转到末页"""
        if self.current_page < self.total_pages:
            self.current_page = self.total_pages
            self._update_pagination_info()
            self._reload_current_page()

    def _reload_current_page(self):
        """重新加载当前页数据"""
        if self.use_sqlite_data and self.sqlite_manager:
            self._load_current_page_sqlite_data()
        else:
            self._load_current_page_data()

    def _on_page_entry_changed(self, event):
        """页码输入框回车事件"""
        try:
            page = int(self.page_var.get())
            if 1 <= page <= self.total_pages:
                self.current_page = page
                self._update_pagination_info()
                self._reload_current_page()
            else:
                # 恢复当前页码
                self.page_var.set(str(self.current_page))
        except ValueError:
            # 恢复当前页码
            self.page_var.set(str(self.current_page))

    def _on_page_size_changed(self, event):
        """每页显示数量改变事件"""
        try:
            if hasattr(self, 'page_size_var') and self.page_size_var:
                self.page_size = int(self.page_size_var.get())
            else:
                self.page_size = 100
        except (AttributeError, ValueError):
            self.page_size = 100

        self.total_pages = (self.total_records + self.page_size - 1) // self.page_size

        # 调整当前页码，确保不超出范围
        if self.current_page > self.total_pages:
            self.current_page = self.total_pages if self.total_pages > 0 else 1

        self._update_pagination_info()
        self._reload_current_page()

    def _sort_by_column(self, column):
        """按列排序"""
        # 获取当前数据
        data = [(self.results_tree.set(item, column), item) for item in self.results_tree.get_children("")]

        # 排序
        data.sort(key=lambda x: x[0])

        # 重新排列
        for index, (val, item) in enumerate(data):
            self.results_tree.move(item, "", index)

        self._log_message(f"按 {column} 列排序")

    def get_data(self):
        """获取页面数据"""
        return {
            "table_name": self.table_name,
            "comparison_data": self.comparison_data,
            "current_page": self.current_page,
            "page_size": self.page_size,
            "total_records": self.total_records
        }

    def _on_v_scroll(self, *args):
        """垂直滚动条回调，智能显示/隐藏滚动条"""
        # 更新滚动条位置
        self.v_scrollbar.set(*args)

        # 检查是否需要显示垂直滚动条
        if args[0] == '0.0' and args[1] == '1.0':
            # 内容完全可见，隐藏滚动条
            self.v_scrollbar.grid_remove()
        else:
            # 内容超出，显示滚动条
            self.v_scrollbar.grid(row=0, column=1, sticky="ns")

    def _on_h_scroll(self, *args):
        """水平滚动条回调，智能显示/隐藏滚动条"""
        # 更新滚动条位置
        self.h_scrollbar.set(*args)

        # 检查是否需要显示水平滚动条
        if args[0] == '0.0' and args[1] == '1.0':
            # 内容完全可见，隐藏滚动条
            self.h_scrollbar.grid_remove()
        else:
            # 内容超出，显示滚动条
            self.h_scrollbar.grid(row=1, column=0, sticky="ew")

    def _on_tree_configure(self, event):
        """表格配置变化时，重新检查滚动条需求"""
        # 延迟执行，确保布局完成
        self.results_tree.after_idle(self._update_scrollbars)

    def _update_scrollbars(self):
        """更新滚动条显示状态"""
        try:
            # 获取当前滚动位置
            v_pos = self.results_tree.yview()
            h_pos = self.results_tree.xview()

            # 更新滚动条状态
            self._on_v_scroll(*v_pos)
            self._on_h_scroll(*h_pos)
        except:
            # 忽略可能的错误
            pass
