#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
比对服务层 - 纯API包装层，委托给SQLAlchemyComparisonService
Backend API作为SQLCompare的服务包装层，不重新实现比对逻辑
"""

import os
import sys
import asyncio
import logging
from typing import Dict, Any, Optional, List
from pathlib import Path

# 确保能导入SQLCompare核心模块
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 直接使用主项目的服务和模型
from services.sqlalchemy_service import SQLAlchemyComparisonService
from models.pydantic_models import TaskCreateFromModel, TaskCreateDirect, TaskResponse
from models.sqlalchemy_models import TaskStatus
from utils.config_manager import SmartConfigManager

logger = logging.getLogger(__name__)


class ComparisonService:
    """
    比对服务类 - 纯API包装层，委托给SQLAlchemyComparisonService

    职责：
    1. 接收API请求参数
    2. 转换为主项目服务所需格式
    3. 委托给SQLAlchemyComparisonService执行
    4. 返回API响应格式

    不负责：
    1. 重新实现比对算法
    2. 重新实现连接器逻辑
    3. 重新定义数据模型
    4. 重新实现业务逻辑
    """

    def __init__(self, database_url: str = "sqlite:///./sqlcompare.db"):
        # 直接使用主项目的SQLAlchemy服务
        self.sqlalchemy_service = SQLAlchemyComparisonService(database_url)
        self.config_manager = SmartConfigManager()

        logger.info("比对服务初始化完成，委托给SQLAlchemyComparisonService")
    
    async def create_task_from_model(self, user_id: str, task_data: TaskCreateFromModel) -> str:
        """
        基于模型创建任务 - 委托给SQLAlchemyComparisonService

        Args:
            user_id: 用户ID
            task_data: 任务创建数据

        Returns:
            任务ID
        """
        try:
            return self.sqlalchemy_service.create_task_from_model(user_id, task_data)
        except Exception as e:
            logger.error(f"基于模型创建任务失败: {e}")
            raise

    async def create_task_from_direct(self, user_id: str, task_data: TaskCreateDirect) -> str:
        """
        直接创建任务 - 委托给SQLAlchemyComparisonService

        Args:
            user_id: 用户ID
            task_data: 任务创建数据

        Returns:
            任务ID
        """
        try:
            return self.sqlalchemy_service.create_task_from_direct(user_id, task_data)
        except Exception as e:
            logger.error(f"直接创建任务失败: {e}")
            raise
    
    async def start_comparison_task(self, task_id: str) -> bool:
        """
        启动比对任务 - 委托给SQLAlchemyComparisonService

        Args:
            task_id: 任务ID

        Returns:
            是否启动成功
        """
        try:
            # 委托给主项目服务执行
            return await self._execute_task_via_service(task_id)
        except Exception as e:
            logger.error(f"启动任务失败: {task_id}, {e}")
            raise
    
    async def _execute_task_via_service(self, task_id: str) -> bool:
        """
        通过主项目服务执行任务
        """
        try:
            logger.info(f"开始执行比对任务: {task_id}")

            # 获取任务信息
            task_summary = self.sqlalchemy_service.get_task_summary(task_id)
            if not task_summary:
                raise ValueError(f"任务不存在: {task_id}")

            # 更新任务状态为运行中
            self.sqlalchemy_service.update_task_status(task_id, TaskStatus.RUNNING.value)

            # 在后台执行任务（这里应该集成主项目的比对引擎）
            asyncio.create_task(self._run_comparison_in_background(task_id))

            logger.info(f"任务启动成功: {task_id}")
            return True

        except Exception as e:
            logger.error(f"执行任务失败: {task_id}, {e}")
            # 更新任务状态为失败
            try:
                self.sqlalchemy_service.update_task_status(task_id, TaskStatus.FAILED.value, str(e))
            except:
                pass
            raise

    async def _run_comparison_in_background(self, task_id: str):
        """
        在后台运行比对任务 - 真正集成主项目的比对引擎
        """
        try:
            logger.info(f"开始执行比对任务: {task_id}")

            # 获取任务配置信息
            task_summary = self.sqlalchemy_service.get_task_summary(task_id)
            if not task_summary:
                raise ValueError(f"任务不存在: {task_id}")

            # 创建临时配置文件供主项目引擎使用
            config_file = await self._create_config_file_from_task(task_id)

            try:
                # 加载配置
                if not self.config_manager.load_config(config_file):
                    raise RuntimeError("配置文件加载失败")

                # 获取比对类型（CMP_TYPE）
                common_config = self.config_manager.config.get('COMMON', {})
                cmp_type = int(common_config.get('cmp_type', '2'))
                table_name = common_config.get('table_id', 'unknown')

                # 创建数据库连接器
                source_connector = self._create_connector('DB1')
                target_connector = self._create_connector('DB2')

                # 创建结果报告器
                from reporters.sqlite_reporter import SqliteReporter
                reporter = SqliteReporter(task_id=task_id)

                # 导入主项目的比对引擎
                from core.engine import compare_sources, compare_sources_memory_dict

                # 根据CMP_TYPE选择算法并执行
                logger.info(f"使用比对算法类型: {cmp_type}")

                if cmp_type == 1:
                    # 流式归并算法 - 适合亿级数据
                    logger.info("执行流式归并算法")
                    await asyncio.get_event_loop().run_in_executor(
                        None,
                        compare_sources,
                        source_connector,
                        target_connector,
                        reporter
                    )
                else:
                    # 内存字典算法 - 适合千万级数据，速度更快
                    logger.info("执行内存字典算法")
                    await asyncio.get_event_loop().run_in_executor(
                        None,
                        compare_sources_memory_dict,
                        source_connector,
                        target_connector,
                        reporter
                    )

                # 更新任务状态为完成
                self.sqlalchemy_service.update_task_status(task_id, TaskStatus.COMPLETED.value)
                logger.info(f"比对任务执行完成: {task_id}")

            finally:
                # 清理临时配置文件
                if os.path.exists(config_file):
                    os.unlink(config_file)
                    logger.debug(f"清理临时配置文件: {config_file}")

        except Exception as e:
            logger.error(f"比对任务执行失败: {task_id}, {e}")
            # 更新任务状态为失败
            try:
                self.sqlalchemy_service.update_task_status(task_id, TaskStatus.FAILED.value, str(e))
            except Exception as update_error:
                logger.error(f"更新任务状态失败: {task_id}, {update_error}")

    async def _create_config_file_from_task(self, task_id: str) -> str:
        """
        从任务信息创建临时配置文件供主项目引擎使用
        """
        import tempfile

        try:
            # 获取任务详细信息（包括连接配置）
            task_summary = self.sqlalchemy_service.get_task_summary(task_id)

            # 这里需要从任务中获取数据库连接配置
            # 目前简化实现，实际应该从任务关联的连接和模型中获取
            source_conn_id = task_summary.get('source_connid')
            target_conn_id = task_summary.get('target_connid')

            if source_conn_id:
                source_conn = self.sqlalchemy_service.get_database_connection(source_conn_id)
            else:
                # 如果是直接创建的任务，从任务配置中获取
                source_conn = task_summary.get('source_conn', {})

            if target_conn_id:
                target_conn = self.sqlalchemy_service.get_database_connection(target_conn_id)
            else:
                target_conn = task_summary.get('target_conn', {})

            # 获取比对类型
            cmp_type = task_summary.get('cmp_type', 2)

            # 生成Config.ini内容
            config_content = f"""[COMMON]
TITLE     = Backend API任务比对
CMP_TYPE  = {cmp_type}
TAB_RULE  = ./rules.xml

[DB1]
TYPE      = {source_conn.get('db_type', 'DB2').upper()}
IP        = {source_conn.get('host', '')}
PORT      = {source_conn.get('port', 50000)}
USER_NAME = {source_conn.get('username', '')}
PASSWORD  = {source_conn.get('password', '')}
SCHEMA    = {source_conn.get('database', '')}

[DB2]
TYPE      = {target_conn.get('db_type', 'DB2').upper()}
IP        = {target_conn.get('host', '')}
PORT      = {target_conn.get('port', 50000)}
USER_NAME = {target_conn.get('username', '')}
PASSWORD  = {target_conn.get('password', '')}
SCHEMA    = {target_conn.get('database', '')}
"""

            # 创建临时配置文件
            fd, config_file = tempfile.mkstemp(suffix='.ini', prefix=f'sqlcompare_task_{task_id}_')
            with os.fdopen(fd, 'w', encoding='utf-8') as f:
                f.write(config_content)

            logger.debug(f"创建临时配置文件: {config_file}")
            return config_file

        except Exception as e:
            logger.error(f"创建配置文件失败: {task_id}, {e}")
            raise

    def _create_connector(self, db_key: str):
        """
        创建数据库连接器 - 使用主项目的连接器
        """
        try:
            # 从配置管理器获取数据库配置
            db_config = self.config_manager.get_database_config(db_key)
            if not db_config:
                raise ValueError(f"数据库配置不存在: {db_key}")

            # 转换为连接器需要的格式
            connector_config = {
                'TYPE': db_config.db_type.upper(),
                'IP': db_config.host,
                'PORT': str(db_config.port),
                'USER_NAME': db_config.username,
                'PASSWORD': db_config.password,
                'SCHEMA': db_config.database
            }

            # 根据数据库类型创建对应的连接器
            db_type = db_config.db_type.lower()

            if db_type == 'db2':
                from connectors.db2_connector import DB2Connector
                return DB2Connector(connector_config)
            elif db_type == 'mysql':
                from connectors.mysql_connector import MySQLConnector
                return MySQLConnector(connector_config)
            elif db_type == 'oracle':
                from connectors.oracle_connector import OracleConnector
                return OracleConnector(connector_config)
            elif db_type == 'postgresql':
                from connectors.postgresql_connector import PostgreSQLConnector
                return PostgreSQLConnector(connector_config)
            elif db_type == 'gaussdb':
                from connectors.gaussdb_connector import GaussDBConnector
                return GaussDBConnector(connector_config)
            else:
                raise ValueError(f"不支持的数据库类型: {db_type}")

        except Exception as e:
            logger.error(f"创建连接器失败: {db_key}, {e}")
            raise
    
    def get_task_summary(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取任务汇总 - 委托给SQLAlchemyComparisonService
        """
        try:
            return self.sqlalchemy_service.get_task_summary(task_id)
        except Exception as e:
            logger.error(f"获取任务汇总失败: {task_id}, {e}")
            return None

    def get_task_progress(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取任务进度 - 委托给SQLAlchemyComparisonService
        """
        try:
            return self.sqlalchemy_service.get_task_progress(task_id)
        except Exception as e:
            logger.error(f"获取任务进度失败: {task_id}, {e}")
            return None

    def get_tasks_by_user(self, user_id: str, status: Optional[str] = None,
                         limit: int = 20, offset: int = 0) -> List[Dict[str, Any]]:
        """
        获取用户任务列表 - 委托给SQLAlchemyComparisonService
        """
        try:
            return self.sqlalchemy_service.get_tasks_by_user(user_id, status, limit, offset)
        except Exception as e:
            logger.error(f"获取用户任务列表失败: {user_id}, {e}")
            return []
    
    def get_comparison_results(self, task_id: str, limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """
        获取比对结果 - 委托给SQLAlchemyComparisonService
        """
        try:
            return self.sqlalchemy_service.get_comparison_results(task_id, limit, offset)
        except Exception as e:
            logger.error(f"获取比对结果失败: {task_id}, {e}")
            return []

    def cancel_task(self, task_id: str) -> bool:
        """
        取消任务 - 委托给SQLAlchemyComparisonService
        """
        try:
            self.sqlalchemy_service.update_task_status(task_id, TaskStatus.CANCELLED.value)
            logger.info(f"任务取消成功: {task_id}")
            return True
        except Exception as e:
            logger.error(f"取消任务失败: {task_id}, {e}")
            return False

    def delete_task(self, task_id: str) -> bool:
        """
        删除任务 - 委托给SQLAlchemyComparisonService
        """
        try:
            return self.sqlalchemy_service.delete_task(task_id)
        except Exception as e:
            logger.error(f"删除任务失败: {task_id}, {e}")
            return False
