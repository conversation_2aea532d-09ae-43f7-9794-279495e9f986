# reporters/asynchronous/csvfile_reporter.py
"""
异步文件对差异报告器
为每个表创建一对文件，用于Beyond Compare等工具进行比较
"""
import asyncio
import os
from typing import List, Dict, Any, Optional

import aiofiles

from .base_reporter import AsyncBaseReporter
from core.models import DiffResult
from models.sqlalchemy_models import DiffStatus


class AsyncFilePairReporter(AsyncBaseReporter):
    """异步文件对差异报告器"""

    def __init__(self, config: Optional[Dict[str, Any]] = None, **kwargs):
        """
        初始化异步文件对报告器

        Args:
            config: 报告器配置，包含output_dir等
            **kwargs: 其他参数
        """
        super().__init__(config, **kwargs)
        self.output_dir = self.config.get('output_dir', './data')
        self.encoding = self.config.get('encoding', 'utf-8')
        self._file_handles: Dict[str, asyncio.StreamWriter] = {}
        self._lock = asyncio.Lock()

    async def initialize(self) -> bool:
        """初始化报告器，创建输出目录"""
        try:
            os.makedirs(self.output_dir, exist_ok=True)
            self.logger.info(f"输出目录已确认: {self.output_dir}")
            return True
        except Exception as e:
            self.logger.error(f"创建输出目录失败: {e}")
            self.stats['last_error'] = str(e)
            return False

    async def finalize(self) -> bool:
        """关闭所有打开的文件"""
        async with self._lock:
            for handle in self._file_handles.values():
                try:
                    handle.close()
                    await handle.wait_closed()
                except Exception as e:
                    self.logger.warning(f"关闭文件句柄时出错: {e}")
            self._file_handles.clear()
            self.logger.info("所有文件句柄已关闭")
        return True

    async def _open_files_for_table(self, table_name: str):
        """为指定的表打开文件对"""
        async with self._lock:
            if table_name not in self._file_handles:
                file1_path = os.path.join(self.output_dir, f"{table_name}_sql_1.txt")
                file2_path = os.path.join(self.output_dir, f"{table_name}_sql_2.txt")

                try:
                    file1_handle = await aiofiles.open(file1_path, 'w', encoding=self.encoding)
                    file2_handle = await aiofiles.open(file2_path, 'w', encoding=self.encoding)
                    self._file_handles[f"{table_name}_1"] = file1_handle
                    self._file_handles[f"{table_name}_2"] = file2_handle
                    self.logger.info(f"为表 '{table_name}' 打开了文件对")
                except Exception as e:
                    self.logger.error(f"为表 '{table_name}' 打开文件失败: {e}")
                    raise

    async def _write_batch_async(self, diffs: List[DiffResult], table_name: str) -> bool:
        """
        异步批量写入差异到文件对

        Args:
            diffs: 差异列表
            table_name: 表名

        Returns:
            写入是否成功
        """
        if not table_name:
            self.logger.warning("table_name为空，跳过写入")
            return True

        try:
            await self._open_files_for_table(table_name)

            file1_handle = self._file_handles[f"{table_name}_1"]
            file2_handle = self._file_handles[f"{table_name}_2"]

            lines1, lines2 = [], []
            for diff in diffs:
                if diff.status != DiffStatus.IDENTICAL:
                    line1 = f"[{diff.key}]{self._format_value(diff.value_a)}\n"
                    line2 = f"[{diff.key}]{self._format_value(diff.value_b)}\n"
                    lines1.append(line1)
                    lines2.append(line2)

            if lines1:
                await file1_handle.write(''.join(lines1))
                await file1_handle.flush()
            if lines2:
                await file2_handle.write(''.join(lines2))
                await file2_handle.flush()

            return True
        except Exception as e:
            self.logger.error(f"向表 '{table_name}' 的文件对写入批量数据失败: {e}")
            self.stats['last_error'] = str(e)
            return False

    def _format_value(self, value) -> str:
        """
        格式化值为字符串

        Args:
            value: 要格式化的值

        Returns:
            格式化后的字符串
        """
        if value is None:
            return ''
        return str(value)
    
    async def get_file_info(self) -> Dict[str, Any]:
        """
        获取文件信息
        
        Returns:
            文件信息字典
        """
        try:
            file_stats = os.stat(self.filepath) if os.path.exists(self.filepath) else None
            
            return {
                'filepath': self.filepath,
                'exists': os.path.exists(self.filepath),
                'size_bytes': file_stats.st_size if file_stats else 0,
                'encoding': self.encoding,
                'delimiter': self.delimiter,
                'field_names': self.field_names,
                'header_written': self._header_written
            }
        except Exception as e:
            self.logger.error(f"获取文件信息失败: {e}")
            return {'error': str(e)}
    
    async def validate_file(self) -> bool:
        """
        验证CSV文件格式
        
        Returns:
            文件是否有效
        """
        try:
            if not os.path.exists(self.filepath):
                return False
            
            # 读取文件的前几行进行验证
            import aiofiles
            async with aiofiles.open(self.filepath, 'r', encoding=self.encoding) as f:
                first_line = await f.readline()
                
                if self.include_header:
                    # 验证头部
                    expected_header = self.delimiter.join(self.field_names)
                    if first_line.strip() != expected_header:
                        self.logger.warning("CSV头部不匹配")
                        return False
                
                # 尝试读取第二行验证格式
                second_line = await f.readline()
                if second_line:
                    fields = second_line.split(self.delimiter)
                    if len(fields) != len(self.field_names):
                        self.logger.warning("CSV字段数量不匹配")
                        return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"CSV文件验证失败: {e}")
            return False


class AsyncFilePairReporterFactory:
    """异步CSV报告器工厂"""
    
    @staticmethod
    def create_reporter(
        filepath: str,
        delimiter: str = ',',
        encoding: str = 'utf-8-sig',
        batch_size: int = 100,
        include_header: bool = True,
        include_timestamp: bool = True,
        field_names: Optional[List[str]] = None
    ) -> AsyncFilePairReporter:
        """
        创建异步CSV报告器
        
        Args:
            filepath: 文件路径
            delimiter: 分隔符
            encoding: 编码
            batch_size: 批量大小
            include_header: 是否包含头部
            include_timestamp: 是否包含时间戳
            field_names: 字段名列表
            
        Returns:
            异步CSV报告器实例
        """
        config = {
            'filepath': filepath,
            'delimiter': delimiter,
            'encoding': encoding,
            'include_header': include_header,
            'include_timestamp': include_timestamp
        }
        
        if field_names:
            config['field_names'] = field_names
        
        return AsyncFilePairReporter(config=config, batch_size=batch_size)
    
    @staticmethod
    def create_excel_compatible_reporter(
        filepath: str,
        batch_size: int = 100
    ) -> AsyncFilePairReporter:
        """
        创建Excel兼容的CSV报告器
        
        Args:
            filepath: 文件路径
            batch_size: 批量大小
            
        Returns:
            Excel兼容的异步CSV报告器
        """
        config = {
            'filepath': filepath,
            'delimiter': ',',
            'encoding': 'utf-8-sig',  # Excel兼容的编码
            'include_header': True,
            'include_timestamp': True,
            'field_names': ['键值', '状态', '源端数据', '目标端数据', '时间戳']
        }
        
        return AsyncFilePairReporter(config=config, batch_size=batch_size)


# 便捷函数
async def create_async_csv_report(
    filepath: str,
    diffs: List[DiffResult],
    **kwargs
) -> bool:
    """
    创建异步CSV报告的便捷函数
    
    Args:
        filepath: 文件路径
        diffs: 差异列表
        **kwargs: 其他参数
        
    Returns:
        报告是否创建成功
    """
    try:
        reporter = AsyncFilePairReporterFactory.create_reporter(filepath, **kwargs)
        
        async with reporter:
            await reporter.report_diffs_async(diffs)
        
        return True
        
    except Exception as e:
        print(f"创建CSV报告失败: {e}")
        return False
