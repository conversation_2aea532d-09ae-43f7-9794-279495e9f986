# core/orchestrator.py
import yaml
import importlib
from typing import Dict, Any
from core.engine import compare_sources

def load_plugin(plugin_type: str, config: Dict[str, Any]):
    """动态加载并实例化插件。"""
    try:
        module_path = config['module']
        class_name = config['class']
        plugin_module = importlib.import_module(module_path)
        plugin_class = getattr(plugin_module, class_name)
        # 传递插件自身的配置
        return plugin_class(**config.get('params', {}))
    except (ImportError, AttributeError, KeyError) as e:
        print(f"加载插件失败: {plugin_type} - {e}")
        raise

def run_comparison_from_config(config_path: str):
    """从 YAML 配置文件加载任务并执行比对。"""
    print(f"从配置文件加载比对任务: {config_path}")
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            task_config = yaml.safe_load(f)
    except (IOError, yaml.YAMLError) as e:
        print(f"读取或解析配置文件失败: {e}")
        raise

    # 加载数据源 A 的连接器
    source_a_config = task_config['source_a']
    connector_a = load_plugin('source', source_a_config)

    # 加载数据源 B 的连接器
    source_b_config = task_config['source_b']
    connector_b = load_plugin('source', source_b_config)

    # 加载报告器
    reporter_config = task_config['reporter']
    reporter = load_plugin('reporter', reporter_config)

    # 获取表名，如果配置中没有，则使用默认值
    table_name = task_config.get('table_name', 'unknown')

    # 执行比对
    compare_sources(connector_a, connector_b, reporter)