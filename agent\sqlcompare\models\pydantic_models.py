"""
Pydantic API接口层
本模块专注于API接口定义，实现关注点分离的架构设计。
包含所有API请求/响应模型定义，不包含数据模型定义和业务逻辑。

职责:
1. 定义API请求模型和验证规则
2. 定义API响应模型和序列化格式
3. 实现数据验证和业务规则检查
4. 提供API文档生成支持

设计原则:
- 单一职责: 只关注API接口定义
- 数据验证: 完善的输入验证机制
- 文档友好: 支持自动API文档生成
- 向后兼容: 保持API接口的稳定性
"""
import base64
from datetime import datetime
from decimal import Decimal
from typing import Dict, List, Any, Optional
from pydantic import BaseModel, Field, field_validator

# ==================== 连接管理API模型 ====================

class ConnectionCreate(BaseModel):
    """创建数据库连接模型 - 增强：添加验证器"""
    name: str = Field(..., description="连接名称")
    type: str = Field(..., description="数据库类型")
    host: str = Field(..., description="主机地址")
    port: int = Field(..., description="端口号")
    username: str = Field(..., description="用户名")
    password: str = Field(..., description="密码")
    database: str = Field(..., description="数据库名")
    dbschema: Optional[str] = Field(None, description="数据库模式名", alias="schema")
    params: Optional[Dict[str, Any]] = Field(None, description="额外参数")

    @field_validator('type')
    @classmethod
    def validate_db_type(cls, v):
        """验证数据库类型，支持主流数据库"""
        allowed_types = ['db2', 'mysql', 'postgresql', 'oracle', 'gaussdb']
        if v.lower() not in allowed_types:
            raise ValueError(f'不支持的数据库类型: {v}，支持的类型: {allowed_types}')
        return v.lower()

    @field_validator('port')
    @classmethod
    def validate_port(cls, v):
        """验证端口号范围"""
        if not 1 <= v <= 65535:
            raise ValueError('端口号必须在1-65535范围内')
        return v

class ConnectionResponse(BaseModel):
    """数据库连接响应模型"""
    id: int
    name: str
    type: str
    host: str
    port: int
    username: str
    password: str
    database: str
    dbschema: Optional[str] = Field(None, alias="schema")
    status: str
    create_time: datetime
    update_time: datetime

    class Config:
        from_attributes = True

# ==================== 模型管理API模型 ====================

class ModelCreate(BaseModel):
    """创建比对模型 - 增强：添加验证器"""
    name: str = Field(..., description="模型名称")
    description: Optional[str] = Field(None, description="模型描述")
    source_connid: int = Field(..., description="源连接ID")
    target_connid: int = Field(..., description="目标连接ID")
    cmp_type: str = Field("content", description="比对类型")
    global_config: Optional[Dict[str, Any]] = Field(None, description="全局配置")

    @field_validator('source_connid', 'target_connid')
    @classmethod
    def validate_connection_ids(cls, v):
        """验证连接ID必须为正数"""
        if v <= 0:
            raise ValueError('连接ID必须大于0')
        return v

    @field_validator('cmp_type')
    @classmethod
    def validate_cmp_type(cls, v):
        """验证比对类型"""
        allowed_types = ['content', 'structure']
        if v not in allowed_types:
            raise ValueError(f'比对类型必须是: {allowed_types}')
        return v

class ModelResponse(BaseModel):
    """比对模型响应"""
    id: int
    name: str
    description: Optional[str]
    source_connid: int
    target_connid: int
    cmp_type: str
    global_config: Optional[Dict[str, Any]]
    status: bool
    create_time: datetime
    update_time: datetime

    # 关联信息
    source_conn: Optional[ConnectionResponse] = None
    target_conn: Optional[ConnectionResponse] = None
    table_rules_count: int = Field(0, description="表规则数量")

    class Config:
        from_attributes = True

# ==================== 表规则管理API模型 ====================

class TableRuleCreate(BaseModel):
    """创建表规则模型 - 增强：添加SQL验证器"""
    table_id: str = Field(..., description="表标识")
    table_name: str = Field(..., description="实际表名")
    sql_1: str = Field(..., description="源端SQL语句")
    sql_2: str = Field(..., description="目标端SQL语句")
    remark: Optional[str] = Field(None, description="备注说明")
    primary_keys: Optional[List[str]] = Field(None, description="主键字段列表")
    ignore_fields: Optional[List[str]] = Field(None, description="忽略字段列表")
    field_mappings: Optional[Dict[str, str]] = Field(None, description="字段映射关系")

    @field_validator('sql_1', 'sql_2')
    @classmethod
    def validate_sql(cls, v):
        """验证SQL语句必须包含KEY字段"""
        if not v.strip():
            raise ValueError('SQL语句不能为空')
        if 'KEY' not in v.upper():
            raise ValueError('SQL语句必须包含KEY字段作为比对键')
        return v.strip()

class TableRuleResponse(BaseModel):
    """表规则响应模型"""
    id: int
    model_id: int
    table_id: str
    table_name: str
    sql_1: str
    sql_2: str
    remark: Optional[str]
    primary_keys: Optional[List[str]]
    ignore_fields: Optional[List[str]]
    field_mappings: Optional[Dict[str, str]]
    status: bool
    create_time: datetime
    update_time: datetime

    class Config:
        from_attributes = True

# ==================== 任务管理API模型 ====================

class TaskCreateFromModel(BaseModel):
    """基于模型创建任务"""
    model_id: int = Field(..., description="比对模型ID")
    task_name: Optional[str] = Field(None, description="任务名称（可覆盖模型名称）")
    selected_table_ids: Optional[List[str]] = Field(None, description="选择执行的表ID列表，不指定则执行所有表")

    @field_validator('model_id')
    @classmethod
    def validate_model_id(cls, v):
        """验证模型ID必须为正数"""
        if v <= 0:
            raise ValueError('模型ID必须大于0')
        return v

    @field_validator('selected_table_ids')
    @classmethod
    def validate_table_ids(cls, v):
        """验证表ID列表的唯一性"""
        if v and len(v) != len(set(v)):
            raise ValueError('table_id必须唯一')
        return v

class TaskCreateDirect(BaseModel):
    """直接创建任务"""
    task_name: Optional[str] = Field(None, description="任务名称")
    comparison_type: str = Field("content", description="比对类型")
    source_conn: Dict[str, Any] = Field(..., description="源数据库连接")
    target_conn: Dict[str, Any] = Field(..., description="目标数据库连接")
    sql_rules: List[TableRuleCreate] = Field(..., description="SQL比对规则列表", min_items=1)

    @field_validator('comparison_type')
    @classmethod
    def validate_comparison_type(cls, v):
        """验证比对类型"""
        allowed_types = ['content', 'structure']
        if v not in allowed_types:
            raise ValueError(f'比对类型必须是: {allowed_types}')
        return v

    def validate_connections(self):
        """验证数据库连接配置"""
        for conn_name, conn in [('source_conn', self.source_conn),
                               ('target_conn', self.target_conn)]:
            required_fields = ['db_type', 'host', 'port', 'database', 'username', 'password']
            for field in required_fields:
                if field not in conn:
                    raise ValueError(f'{conn_name} missing required field: {field}')

    def validate_sql_rules_uniqueness(self):
        """验证SQL规则"""
        if not self.sql_rules:
            raise ValueError('至少需要一个SQL规则')

        # 检查table_id唯一性
        table_ids = [rule.table_id for rule in self.sql_rules]
        if len(table_ids) != len(set(table_ids)):
            raise ValueError('table_id必须唯一')

class TaskResponse(BaseModel):
    """
    任务响应模型 - 反映单表设计

    支持两种响应模式：
    1. 单个执行记录响应（直接来自ComparisonTask记录）
    2. 任务汇总响应（通过get_task_summary聚合多个执行记录）
    """
    # 任务基本信息
    task_id: str
    task_name: Optional[str]
    user_id: str
    model_id: int

    # 执行信息（可能是单个执行或聚合信息）
    status: str
    progress_pct: Decimal
    current_step: Optional[str]

    # 时间戳
    create_time: datetime
    start_time: Optional[datetime]
    complete_time: Optional[datetime]
    update_time: Optional[datetime]

    # 统计信息（可能是单个执行或聚合信息）
    total_records: int
    processed_records: int
    diff_records: int
    source_only: int
    target_only: int
    exec_time: Decimal

    # 错误信息
    error_msg: Optional[str] = None

    # 关联信息
    model: Optional[ModelResponse] = None

    # 数据清理配置
    retention_days: int = 30
    auto_cleanup: bool = True

    class Config:
        from_attributes = True

# ==================== 结果管理API模型 ====================

class ResultResponse(BaseModel):
    """比对结果响应模型"""
    id: int
    task_id: str
    record_key: str
    table_name: str
    difference_type: str
    source_value: Optional[str]
    target_value: Optional[str]
    field_name: Optional[str]
    created_at: datetime

    class Config:
        from_attributes = True

class TaskExecutionInfo(BaseModel):
    """任务执行信息响应模型"""
    # 任务基本信息
    task_id: str = Field(..., description="任务ID")
    task_name: Optional[str] = Field(None, description="任务名称")
    user_id: str = Field(..., description="用户ID")
    model_id: Optional[int] = Field(None, description="比对模型ID")

    # 执行状态信息
    status: str = Field(..., description="任务状态")
    progress_pct: Decimal = Field(0.0, description="执行进度百分比")
    current_step: Optional[str] = Field(None, description="当前执行步骤")

    # 时间信息
    create_time: datetime = Field(..., description="创建时间")
    start_time: Optional[datetime] = Field(None, description="开始时间")
    complete_time: Optional[datetime] = Field(None, description="完成时间")
    update_time: Optional[datetime] = Field(None, description="更新时间")
    duration: Optional[Decimal] = Field(None, description="执行持续时间（秒）")

    # 统计信息
    total_records: int = Field(0, description="总记录数")
    processed_records: int = Field(0, description="已处理记录数")
    matched_records: int = Field(0, description="匹配记录数")
    diff_records: int = Field(0, description="差异记录数")
    source_only: int = Field(0, description="仅源端记录数")
    target_only: int = Field(0, description="仅目标端记录数")
    exec_time: Decimal = Field(0.0, description="执行时间（秒）")

    # 错误信息
    error_msg: Optional[str] = Field(None, description="错误消息")

    # 配置信息概要
    config_summary: Optional[Dict[str, Any]] = Field(None, description="配置信息概要")

    # 关联信息
    model: Optional[ModelResponse] = Field(None, description="关联的比对模型")
    table_rules: List[Dict[str, Any]] = Field(default_factory=list, description="表规则列表")

    class Config:
        from_attributes = True
        arbitrary_types_allowed = True

    def get_source_config(self) -> Dict[str, Any]:
        """获取源数据库配置"""
        if self.model and hasattr(self.model, 'source_conn') and self.model.source_conn:
            conn = self.model.source_conn

            return {
                'type': conn.type if hasattr(conn, 'type') else 'db2',
                'ip': conn.host if hasattr(conn, 'host') else 'localhost',
                'port': conn.port if hasattr(conn, 'port') else 50000,
                'user_name': conn.username if hasattr(conn, 'username') else '',
                'password': base64.b64decode(conn.password).decode('utf-8'),
                'database': conn.database if hasattr(conn, 'database') else '',
                'schema': conn.database if hasattr(conn, 'database') else ''
            }
        else:
            return None

    def get_target_config(self) -> Dict[str, Any]:
        """获取目标数据库配置"""
        if self.model and hasattr(self.model, 'target_conn') and self.model.target_conn:
            conn = self.model.target_conn

            return {
                'type': conn.type if hasattr(conn, 'type') else 'db2',
                'ip': conn.host if hasattr(conn, 'host') else 'localhost',
                'port': conn.port if hasattr(conn, 'port') else 50000,
                'user_name': conn.username if hasattr(conn, 'username') else '',
                'password': base64.b64decode(conn.password).decode('utf-8'),
                'database': conn.database if hasattr(conn, 'database') else '',
                'schema': conn.database if hasattr(conn, 'database') else ''
            }
        else:
            return None

    def get_comparison_type(self) -> int:
        """获取比对算法类型"""
        if self.model and hasattr(self.model, 'cmp_type'):
            cmp_type = self.model.cmp_type if self.model.cmp_type else 'content'
            return 2 if cmp_type == 'content' else 1
        elif self.config_summary and 'comparison_type' in self.config_summary:
            return 2 if self.config_summary['comparison_type'] == 'content' else 1
        return 2

    def get_summary(self) -> str:
        """获取任务摘要"""
        return f"任务: {self.task_name}, 状态: {self.status}, 进度: {self.progress_pct}%"
    
    def get_stats(self) -> Dict[str, Any]:
        """获取任务统计信息"""
        return {
            'total_records': 0,
            'processed_records': 0,
            'matched_records': 0,
            'diff_records': 0,
            'source_only': 0,
            'target_only': 0,
            'exec_time': 0.0
        }

    def get_rules_list(self) -> List[Dict[str, Any]]:
        """获取表规则列表"""
        return self.table_rules or []

    @property
    def is_single_table_task(self) -> bool:
        """判断是否为单表任务"""
        return len(self.get_rules_list()) == 1

class TaskProgressResponse(BaseModel):
    """任务进度响应模型"""
    task_id: str
    status: str
    progress_pct: Decimal
    current_step: Optional[str]
    processed_records: int
    total_records: int
    start_time: Optional[datetime]
    estimated_completion: Optional[datetime]
    error_msg: Optional[str]

class ComparisonResultResponse(BaseModel):
    """比对结果响应模型 - 精简设计，移除冗余元数据"""
    id: int
    task_id: str
    table_name: str
    record_key: str
    status: str
    field_name: Optional[str]
    source_value: Optional[str]
    target_value: Optional[str]
    diff_type: Optional[str]

    class Config:
        from_attributes = True

class TaskStatistics(BaseModel):
    """任务统计模型"""
    total_tasks: int = Field(..., description="总任务数")
    pending_tasks: int = Field(..., description="待执行任务数")
    running_tasks: int = Field(..., description="运行中任务数")
    completed_tasks: int = Field(..., description="已完成任务数")
    failed_tasks: int = Field(..., description="失败任务数")
    cancelled_tasks: int = Field(..., description="已取消任务数")
    average_execution_time: float = Field(..., description="平均执行时间")
    success_rate: float = Field(..., description="成功率")

# ==================== 通用API模型 ====================

class PaginatedResponse(BaseModel):
    """分页响应模型"""
    items: List[Any] = Field(..., description="数据项")
    total: int = Field(..., description="总数")
    page: int = Field(..., description="当前页")
    page_size: int = Field(..., description="页大小")
    total_pages: int = Field(..., description="总页数")

class ApiResponse(BaseModel):
    """API统一响应模型"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    data: Optional[Any] = Field(None, description="响应数据")
    error_code: Optional[str] = Field(None, description="错误代码")
    timestamp: datetime = Field(default_factory=datetime.now, description="时间戳")

# ==================== 用户管理API模型 ====================

class UserCreate(BaseModel):
    """创建用户模型"""
    username: str = Field(..., description="用户名")
    email: str = Field(..., description="邮箱地址")
    password: str = Field(..., description="密码")
    role: str = Field("user", description="用户角色")

    @field_validator('email')
    @classmethod
    def validate_email(cls, v):
        """验证邮箱格式"""
        import re
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(pattern, v):
            raise ValueError('邮箱格式不正确')
        return v.lower()

class UserResponse(BaseModel):
    """用户响应模型"""
    user_id: str
    username: str
    email: str
    role: str
    is_active: bool
    create_time: datetime
    last_login_time: Optional[datetime]

    class Config:
        from_attributes = True

# 导出接口
__all__ = [
    'ConnectionCreate', 'ConnectionResponse',
    'ModelCreate', 'ModelResponse',
    'TableRuleCreate', 'TableRuleResponse',
    'TaskCreateFromModel', 'TaskCreateDirect', 'TaskResponse',
    'ResultResponse', 'TaskProgressResponse', 'ComparisonResultResponse',
    'TaskExecutionInfo', 'TaskStatistics',
    'PaginatedResponse', 'ApiResponse',
    'UserCreate', 'UserResponse'
]
