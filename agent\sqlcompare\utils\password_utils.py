# password_utils.py - 密码加密解密工具模块
import os
import base64
import logging
from typing import Optional
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

logger = logging.getLogger(__name__)


class PasswordCrypto:
    """密码加密解密工具类"""
    
    def __init__(self, key: Optional[bytes] = None):
        """
        初始化密码加密工具
        
        Args:
            key: 加密密钥，如果为None则从环境变量或默认值获取
        """
        self._key = key or self._get_encryption_key()
        self._fernet = Fernet(self._key)
    
    def _get_encryption_key(self) -> bytes:
        """
        获取加密密钥
        
        Returns:
            加密密钥
        """
        # 优先从环境变量获取
        env_key = os.getenv('SQLCOMPARE_ENCRYPTION_KEY')
        if env_key:
            try:
                return base64.urlsafe_b64decode(env_key.encode())
            except Exception as e:
                logger.warning(f"环境变量中的加密密钥无效: {e}")
        
        # 从配置文件获取或生成默认密钥
        return self._generate_key_from_seed()
    
    def _generate_key_from_seed(self) -> bytes:
        """
        从种子生成密钥（用于向后兼容）
        
        Returns:
            生成的密钥
        """
        # 使用固定的种子生成密钥（生产环境应该使用更安全的方式）
        seed = os.getenv('SQLCOMPARE_SEED', 'sqlcompare_default_seed_2024')
        salt = b'sqlcompare_salt_v4'
        
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(seed.encode()))
        return key
    
    def encrypt_password(self, password: str) -> str:
        """
        加密密码
        
        Args:
            password: 明文密码
            
        Returns:
            加密后的密码（带前缀）
        """
        if not password:
            return ''
        
        try:
            encrypted_bytes = self._fernet.encrypt(password.encode())
            encrypted_str = base64.urlsafe_b64encode(encrypted_bytes).decode()
            return f"enc:{encrypted_str}"
        except Exception as e:
            logger.error(f"密码加密失败: {e}")
            raise
    
    def decrypt_password(self, encrypted_password: str) -> str:
        """
        解密密码
        
        Args:
            encrypted_password: 加密的密码
            
        Returns:
            明文密码
        """
        if not encrypted_password:
            return ''
        
        try:
            # 移除前缀
            if encrypted_password.startswith('enc:'):
                encrypted_data = encrypted_password[4:]
            else:
                # 可能是其他格式的加密密码，尝试直接解密
                encrypted_data = encrypted_password
            
            # 解码并解密
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_data.encode())
            decrypted_bytes = self._fernet.decrypt(encrypted_bytes)
            return decrypted_bytes.decode()
            
        except Exception as e:
            logger.error(f"密码解密失败: {e}")
            # 解密失败时返回原密码（可能是明文）
            return encrypted_password
    
    def is_encrypted(self, password: str) -> bool:
        """
        判断密码是否已加密
        
        Args:
            password: 密码字符串
            
        Returns:
            True表示已加密，False表示明文
        """
        if not password:
            return False
        
        # 检查加密前缀
        if password.startswith('enc:'):
            return True
        
        # 尝试解密来判断（不推荐，但可以作为后备方案）
        try:
            encrypted_bytes = base64.urlsafe_b64decode(password.encode())
            self._fernet.decrypt(encrypted_bytes)
            return True
        except:
            return False


# 全局密码加密工具实例
_password_crypto = None


def get_password_crypto() -> PasswordCrypto:
    """获取密码加密工具实例（单例模式）"""
    global _password_crypto
    if _password_crypto is None:
        _password_crypto = PasswordCrypto()
    return _password_crypto


def encrypt_password(password: str) -> str:
    """
    加密密码（便捷函数）
    
    Args:
        password: 明文密码
        
    Returns:
        加密后的密码
    """
    return get_password_crypto().encrypt_password(password)


def decrypt_password(encrypted_password: str) -> str:
    """
    解密密码（便捷函数）
    
    Args:
        encrypted_password: 加密的密码
        
    Returns:
        明文密码
    """
    return get_password_crypto().decrypt_password(encrypted_password)


def is_encrypted_password(password: str) -> bool:
    """
    判断密码是否已加密（便捷函数）
    
    Args:
        password: 密码字符串
        
    Returns:
        True表示已加密，False表示明文
    """
    return get_password_crypto().is_encrypted(password)


def migrate_plain_passwords_to_encrypted():
    """
    将数据库中的明文密码迁移为加密密码
    
    这个函数应该在系统升级时运行一次
    """
    try:
        from ..services.sqlalchemy_service import SQLAlchemyComparisonService
        from ..models.sqlalchemy_models import ComparisonConnection
        
        service = SQLAlchemyComparisonService.get_instance()
        crypto = get_password_crypto()
        
        with service.get_db_session() as session:
            connections = session.query(ComparisonConnection).all()
            updated_count = 0
            
            for conn in connections:
                if conn.password and not crypto.is_encrypted(conn.password):
                    # 加密明文密码
                    encrypted_password = crypto.encrypt_password(conn.password)
                    conn.password = encrypted_password
                    updated_count += 1
                    logger.info(f"已加密连接 {conn.name} 的密码")
            
            if updated_count > 0:
                session.commit()
                logger.info(f"密码迁移完成，共更新 {updated_count} 个连接的密码")
            else:
                logger.info("没有需要迁移的明文密码")
                
    except Exception as e:
        logger.error(f"密码迁移失败: {e}")
        raise


def generate_new_encryption_key() -> str:
    """
    生成新的加密密钥
    
    Returns:
        Base64编码的密钥字符串
    """
    key = Fernet.generate_key()
    return base64.urlsafe_b64encode(key).decode()


# 使用示例和测试函数
def test_password_encryption():
    """测试密码加密解密功能"""
    test_passwords = [
        'simple_password',
        'complex_P@ssw0rd!',
        '中文密码测试',
        '',
        'very_long_password_with_special_characters_!@#$%^&*()',
    ]
    
    crypto = get_password_crypto()
    
    for password in test_passwords:
        print(f"原始密码: '{password}'")
        
        if password:
            # 加密
            encrypted = crypto.encrypt_password(password)
            print(f"加密后: {encrypted}")
            
            # 解密
            decrypted = crypto.decrypt_password(encrypted)
            print(f"解密后: '{decrypted}'")
            
            # 验证
            assert password == decrypted, f"密码加密解密失败: {password} != {decrypted}"
            assert crypto.is_encrypted(encrypted), f"加密检测失败: {encrypted}"
            assert not crypto.is_encrypted(password), f"明文检测失败: {password}"
            
            print("✓ 测试通过")
        else:
            print("✓ 空密码跳过")
        
        print("-" * 50)
    
    print("所有密码加密解密测试通过！")


if __name__ == '__main__':
    # 运行测试
    test_password_encryption()
