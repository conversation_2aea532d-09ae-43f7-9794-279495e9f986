@echo off
chcp 65001 >nul
REM SQLCompare Cython Engine Installation Script (Windows)
echo ========================================
echo SQLCompare Cython Engine Installer
echo ========================================

set RC_PATH=C:\Program Files (x86)\Windows Kits\10\bin\10.0.20348.0\x64
set PATH=%RC_PATH%;%PATH%

REM Check Python environment
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python not found
    echo Please ensure Python is installed and added to PATH
    pause
    exit /b 1
)

echo Current Python version:
python --version

REM Install dependencies
echo.
echo [1/3] Installing dependencies...
echo Installing Cython...
python -m pip install cython
if errorlevel 1 (
    echo ERROR: Cython installation failed
    pause
    exit /b 1
)

echo Installing NumPy...
python -m pip install numpy
if errorlevel 1 (
    echo ERROR: NumPy installation failed
    pause
    exit /b 1
)

REM Check compiler
echo.
echo [2/3] Checking compiler environment...
python -c "import distutils.util; print('Compiler check passed')" 2>nul
if errorlevel 1 (
    echo WARNING: Compiler environment may have issues
    echo Please ensure Visual Studio Build Tools is installed
    echo Download: https://visualstudio.microsoft.com/visual-cpp-build-tools/
)

REM Compile Cython extension
echo.
echo [3/3] Compiling Cython extension...
python setup_cython.py build_ext --inplace
if errorlevel 1 (
    echo ERROR: Cython extension compilation failed
    echo.
    echo Troubleshooting suggestions:
    echo 1. Install Visual Studio Build Tools
    echo 2. Check Python version compatibility
    echo 3. Try reinstalling Cython and NumPy
    pause
    exit /b 1
)

echo.
echo ========================================
echo SUCCESS: Cython engine installed!
echo ========================================
echo.
echo Now you can enjoy high-performance data comparison!
echo Expected performance improvement: 30-50%%
echo.
echo Usage:
echo   orchestrator = ComparisonTaskOrchestrator(use_cython=True)
echo.


echo.
echo ========================================
echo DELETE: Cython engine temporary files!
echo ========================================
echo.
if exist "build" (
    echo   deleting build/ directory...
    rmdir /s /q "build"
)
if exist "core\engine_cython.html" (
    echo   deleting core\engine_cython.html
    del "core\engine_cython.html"
)
if exist "core\engine_cython.c" (
    echo   deleting core\engine_cython.c
    del "core\engine_cython.c"
)

echo.
pause
