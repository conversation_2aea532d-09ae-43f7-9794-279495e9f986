-- PostgreSQL原生驱动优化脚本
-- 专门针对原生psycopg2驱动的数据库结构和性能优化

-- =====================================================
-- 1. 创建优化的比对结果表
-- =====================================================

-- 删除现有表（如果存在）
DROP TABLE IF EXISTS comparison_results_optimized CASCADE;

-- 创建优化的表结构
CREATE TABLE comparison_results_optimized (
    id BIGSERIAL PRIMARY KEY,
    task_id VARCHAR(255) NOT NULL,
    table_name VARCHAR(255) NOT NULL,
    record_key TEXT NOT NULL,
    status VARCHAR(50) NOT NULL,
    field_name VARCHAR(255),
    source_value TEXT,
    target_value TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 2. 创建高性能索引策略
-- =====================================================

-- 基础索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_opt_task_id 
ON comparison_results_optimized(task_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_opt_table_name 
ON comparison_results_optimized(table_name);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_opt_status 
ON comparison_results_optimized(status);

-- 复合索引（针对常见查询模式）
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_opt_task_table 
ON comparison_results_optimized(task_id, table_name);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_opt_task_status 
ON comparison_results_optimized(task_id, status);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_opt_task_table_status 
ON comparison_results_optimized(task_id, table_name, status);

-- 覆盖索引（避免回表查询）
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_opt_covering 
ON comparison_results_optimized(task_id, table_name, status) 
INCLUDE (record_key, field_name);

-- 时间索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_opt_created_at 
ON comparison_results_optimized(created_at);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_opt_task_time 
ON comparison_results_optimized(task_id, created_at);

-- =====================================================
-- 3. 创建分区表（适用于超大数据量）
-- =====================================================

-- 创建分区表
CREATE TABLE IF NOT EXISTS comparison_results_partitioned (
    id BIGSERIAL,
    task_id VARCHAR(255) NOT NULL,
    table_name VARCHAR(255) NOT NULL,
    record_key TEXT NOT NULL,
    status VARCHAR(50) NOT NULL,
    field_name VARCHAR(255),
    source_value TEXT,
    target_value TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) PARTITION BY HASH (task_id);

-- 创建4个分区
CREATE TABLE IF NOT EXISTS comparison_results_part_0 PARTITION OF comparison_results_partitioned
FOR VALUES WITH (modulus 4, remainder 0);

CREATE TABLE IF NOT EXISTS comparison_results_part_1 PARTITION OF comparison_results_partitioned
FOR VALUES WITH (modulus 4, remainder 1);

CREATE TABLE IF NOT EXISTS comparison_results_part_2 PARTITION OF comparison_results_partitioned
FOR VALUES WITH (modulus 4, remainder 2);

CREATE TABLE IF NOT EXISTS comparison_results_part_3 PARTITION OF comparison_results_partitioned
FOR VALUES WITH (modulus 4, remainder 3);

-- 为每个分区创建索引
CREATE INDEX IF NOT EXISTS idx_part_0_task_table ON comparison_results_part_0(task_id, table_name);
CREATE INDEX IF NOT EXISTS idx_part_1_task_table ON comparison_results_part_1(task_id, table_name);
CREATE INDEX IF NOT EXISTS idx_part_2_task_table ON comparison_results_part_2(task_id, table_name);
CREATE INDEX IF NOT EXISTS idx_part_3_task_table ON comparison_results_part_3(task_id, table_name);

CREATE INDEX IF NOT EXISTS idx_part_0_status ON comparison_results_part_0(status);
CREATE INDEX IF NOT EXISTS idx_part_1_status ON comparison_results_part_1(status);
CREATE INDEX IF NOT EXISTS idx_part_2_status ON comparison_results_part_2(status);
CREATE INDEX IF NOT EXISTS idx_part_3_status ON comparison_results_part_3(status);

-- =====================================================
-- 4. 创建汇总表（提升查询性能）
-- =====================================================

CREATE TABLE IF NOT EXISTS comparison_result_summary (
    id BIGSERIAL PRIMARY KEY,
    task_id VARCHAR(255) NOT NULL UNIQUE,
    table_name VARCHAR(255) NOT NULL,
    total_records BIGINT DEFAULT 0,
    source_only_count BIGINT DEFAULT 0,
    target_only_count BIGINT DEFAULT 0,
    different_count BIGINT DEFAULT 0,
    identical_count BIGINT DEFAULT 0,
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    duration_seconds BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 汇总表索引
CREATE INDEX IF NOT EXISTS idx_summary_task ON comparison_result_summary(task_id);
CREATE INDEX IF NOT EXISTS idx_summary_table ON comparison_result_summary(table_name);
CREATE INDEX IF NOT EXISTS idx_summary_created ON comparison_result_summary(created_at);

-- =====================================================
-- 5. 原生驱动专用的存储过程
-- =====================================================

-- 批量插入优化存储过程
CREATE OR REPLACE FUNCTION native_batch_insert_comparison_results(
    p_task_id VARCHAR(255),
    p_table_name VARCHAR(255),
    p_records JSONB
) RETURNS TABLE(inserted_count INTEGER, execution_time NUMERIC) AS $$
DECLARE
    start_time TIMESTAMP;
    end_time TIMESTAMP;
    inserted_rows INTEGER;
BEGIN
    start_time := clock_timestamp();
    
    -- 使用高效的INSERT...SELECT语句
    INSERT INTO comparison_results_optimized 
    (task_id, table_name, record_key, status, field_name, source_value, target_value)
    SELECT 
        p_task_id,
        p_table_name,
        record->>'record_key',
        record->>'status',
        record->>'field_name',
        record->>'source_value',
        record->>'target_value'
    FROM jsonb_array_elements(p_records) AS record;
    
    GET DIAGNOSTICS inserted_rows = ROW_COUNT;
    end_time := clock_timestamp();
    
    RETURN QUERY SELECT inserted_rows, EXTRACT(EPOCH FROM (end_time - start_time))::NUMERIC;
END;
$$ LANGUAGE plpgsql;

-- 任务数据清理存储过程
CREATE OR REPLACE FUNCTION native_clear_task_data(
    p_task_id VARCHAR(255),
    p_table_name VARCHAR(255) DEFAULT 'comparison_results_optimized'
) RETURNS INTEGER AS $$
DECLARE
    deleted_rows INTEGER;
BEGIN
    EXECUTE format('DELETE FROM %I WHERE task_id = $1', p_table_name) USING p_task_id;
    GET DIAGNOSTICS deleted_rows = ROW_COUNT;
    
    RETURN deleted_rows;
END;
$$ LANGUAGE plpgsql;

-- 统计信息更新存储过程
CREATE OR REPLACE FUNCTION update_comparison_statistics(p_task_id VARCHAR(255))
RETURNS VOID AS $$
BEGIN
    INSERT INTO comparison_result_summary (
        task_id, table_name, total_records, source_only_count, 
        target_only_count, different_count, start_time, end_time
    )
    SELECT 
        task_id,
        table_name,
        COUNT(*) as total_records,
        SUM(CASE WHEN status = 'SOURCE_ONLY' THEN 1 ELSE 0 END) as source_only_count,
        SUM(CASE WHEN status = 'TARGET_ONLY' THEN 1 ELSE 0 END) as target_only_count,
        SUM(CASE WHEN status = 'DIFFERENT' THEN 1 ELSE 0 END) as different_count,
        MIN(created_at) as start_time,
        MAX(created_at) as end_time
    FROM comparison_results_optimized
    WHERE task_id = p_task_id
    GROUP BY task_id, table_name
    ON CONFLICT (task_id) DO UPDATE SET
        total_records = EXCLUDED.total_records,
        source_only_count = EXCLUDED.source_only_count,
        target_only_count = EXCLUDED.target_only_count,
        different_count = EXCLUDED.different_count,
        start_time = EXCLUDED.start_time,
        end_time = EXCLUDED.end_time,
        updated_at = CURRENT_TIMESTAMP;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 6. 原生驱动性能优化配置
-- =====================================================

-- 针对原生驱动的PostgreSQL配置优化
ALTER SYSTEM SET shared_buffers = '512MB';
ALTER SYSTEM SET effective_cache_size = '2GB';
ALTER SYSTEM SET work_mem = '64MB';
ALTER SYSTEM SET maintenance_work_mem = '1GB';

-- 写入性能优化
ALTER SYSTEM SET wal_buffers = '64MB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET max_wal_size = '4GB';
ALTER SYSTEM SET min_wal_size = '1GB';

-- 连接和并发优化
ALTER SYSTEM SET max_connections = 100;
ALTER SYSTEM SET max_worker_processes = 8;
ALTER SYSTEM SET max_parallel_workers = 6;
ALTER SYSTEM SET max_parallel_workers_per_gather = 3;

-- COPY操作优化
ALTER SYSTEM SET max_locks_per_transaction = 256;
ALTER SYSTEM SET deadlock_timeout = '1s';

-- 自动清理优化（针对大批量插入）
ALTER SYSTEM SET autovacuum_max_workers = 6;
ALTER SYSTEM SET autovacuum_naptime = '15s';
ALTER SYSTEM SET autovacuum_vacuum_scale_factor = 0.05;
ALTER SYSTEM SET autovacuum_analyze_scale_factor = 0.02;

-- =====================================================
-- 7. 创建性能监控视图
-- =====================================================

-- 原生驱动性能监控视图
CREATE OR REPLACE VIEW native_performance_metrics AS
SELECT 
    schemaname,
    tablename,
    n_tup_ins as inserts,
    n_tup_upd as updates,
    n_tup_del as deletes,
    n_live_tup as live_tuples,
    n_dead_tup as dead_tuples,
    last_vacuum,
    last_autovacuum,
    last_analyze,
    last_autoanalyze
FROM pg_stat_user_tables
WHERE tablename LIKE 'comparison_results%'
ORDER BY n_tup_ins DESC;

-- 索引使用情况监控
CREATE OR REPLACE VIEW native_index_usage AS
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_tup_read,
    idx_tup_fetch,
    idx_scan,
    CASE 
        WHEN idx_scan = 0 THEN 'UNUSED'
        WHEN idx_scan < 100 THEN 'LOW_USAGE'
        WHEN idx_scan < 1000 THEN 'MEDIUM_USAGE'
        ELSE 'HIGH_USAGE'
    END as usage_level
FROM pg_stat_user_indexes
WHERE tablename LIKE 'comparison_results%'
ORDER BY idx_scan DESC;

-- 连接池状态监控
CREATE OR REPLACE VIEW native_connection_stats AS
SELECT 
    state,
    COUNT(*) as connection_count,
    MAX(state_change) as last_state_change
FROM pg_stat_activity
WHERE application_name LIKE 'SQLCompare_Native%'
GROUP BY state
ORDER BY connection_count DESC;

-- =====================================================
-- 8. 数据维护脚本
-- =====================================================

-- 手动清理和优化
CREATE OR REPLACE FUNCTION maintain_comparison_tables()
RETURNS TEXT AS $$
DECLARE
    result_text TEXT := '';
BEGIN
    -- 清理过期数据（超过30天）
    DELETE FROM comparison_results_optimized 
    WHERE created_at < CURRENT_DATE - INTERVAL '30 days';
    
    result_text := result_text || 'Cleaned old data. ';
    
    -- 更新表统计信息
    ANALYZE comparison_results_optimized;
    ANALYZE comparison_result_summary;
    
    result_text := result_text || 'Updated statistics. ';
    
    -- 重建索引（如果需要）
    REINDEX TABLE comparison_results_optimized;
    
    result_text := result_text || 'Reindexed tables. ';
    
    RETURN result_text || 'Maintenance completed.';
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 9. 应用配置
-- =====================================================

-- 重新加载配置
SELECT pg_reload_conf();

-- 显示关键配置
SELECT name, setting, unit, context, pending_restart
FROM pg_settings 
WHERE name IN (
    'shared_buffers', 'effective_cache_size', 'work_mem', 
    'maintenance_work_mem', 'wal_buffers', 'max_connections',
    'max_wal_size', 'checkpoint_completion_target'
)
ORDER BY name;

-- 显示表和索引信息
SELECT 
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE tablename LIKE 'comparison_results%'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- 完成提示
SELECT 'PostgreSQL原生驱动优化配置完成！' as status;
