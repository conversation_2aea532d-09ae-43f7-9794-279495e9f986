# main_v4_refactored.py - v4.0架构完整任务管理工作流
import os
import sys
import yaml
import time
import logging
from typing import Dict, Any, Optional
# 核心组件导入
from utils.config_manager import SmartConfigManager
from connectors.db2_connector import DB2Connector
from reporters.reporter_factory import ReporterFactory
from core.engine import compare_sources, compare_sources_memory_dict
from core.engine_hybrid import compare_sources_hybrid, compare_sources_memory_dict_hybrid, get_engine_info
# v4.0架构组件导入
from services.sqlalchemy_service import SQLAlchemyComparisonService
from models.pydantic_models import TaskCreateDirect, TableRuleCreate
from models.sqlalchemy_models import TaskStatus

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - [%(name)s] - %(message)s')
logger = logging.getLogger(__name__)


class ComparisonTaskOrchestrator:
    """比对任务编排器"""

    def __init__(self, report_config: Optional[Dict[str, Any]] = None, use_cython: bool = True):
        """初始化任务编排器"""

        self.batch_size = 10000
        self.reporter_config = report_config or {'type': 'sqlite'}
        self.sqlalchemy_service = SQLAlchemyComparisonService.get_instance(self.reporter_config)
        self.use_cython = use_cython

        # 输出引擎信息
        engine_info = get_engine_info()
        if engine_info['cython_available'] and use_cython:
            logger.info("✅ 使用Cython优化引擎，预期性能提升30-50%")
        else:
            logger.info("📝 使用Python引擎")
            if not engine_info['cython_available']:
                logger.info("💡 提示：安装Cython引擎可获得更好性能，运行: python setup_cython.py build_ext --inplace")

    def create_task_from_config(self, config_manager: SmartConfigManager, user_name: str = "system") -> str:
        """从配置文件创建任务"""
        logger.info("开始从配置文件创建任务...")

        # 1. 配置已由外部加载，直接使用
        if not config_manager.config:
            raise ValueError("传入的配置管理器未加载任何配置")

        # 2. 解析配置数据
        try:
            db1_config = dict(config_manager.config['DB1'])
            db2_config = dict(config_manager.config['DB2'])

            # 解析比对规则
            table_rules = []
            if config_manager.rules is not None:
                for table_elem in config_manager.rules.findall('table'):
                    sql1_elem = table_elem.find('sql_1')
                    sql2_elem = table_elem.find('sql_2')

                    if sql1_elem is not None and sql2_elem is not None:
                        table_rule = TableRuleCreate(
                            table_id=table_elem.get('table_id', f'table_{len(table_rules)}'),
                            table_name=table_elem.get('remark', f'table_{len(table_rules)}'),
                            sql_1=sql1_elem.text.strip(),
                            sql_2=sql2_elem.text.strip()
                        )
                        table_rules.append(table_rule)

            if not table_rules:
                raise ValueError("在规则文件中找不到有效的表规则")

        except (KeyError, AttributeError) as e:
            raise ValueError(f"配置文件格式错误: {e}")
        
        # 3. 创建TaskCreateDirect对象
        task_data = TaskCreateDirect(
            comparison_type="content",
            source_conn=db1_config,
            target_conn=db2_config,
            sql_rules=table_rules
        )

        # 4. 使用SQLAlchemy服务创建任务
        task_id = self.sqlalchemy_service.create_task_from_direct(user_name=user_name, task_data=task_data)

        return task_id

    def create_task_from_model(self,  model_id: int, task_name: str = None, user_name: str = "system") -> str:
        """从模型创建任务"""
        return self.sqlalchemy_service.create_task_from_model(user_name=user_name, model_id=model_id, task_name=task_name)

    def execute_task_from_model(self, task_id: str, progress_callback: Optional[callable] = None) -> bool:
        """执行比对任务"""

        # 记录任务开始时间
        task_start_time = time.perf_counter()

        try:
            # 1. 更新任务状态为运行中
            self.sqlalchemy_service.update_task_status(task_id, TaskStatus.RUNNING)
            self.sqlalchemy_service.update_task_progress(task_id, 0.0, "初始化任务")

            # 2. 一次性取任务执行信息
            execution_info = self.sqlalchemy_service.get_task_execution_info(task_id)
            if not execution_info:
                return False

            # 3. 获取兼容格式的配置信息
            src_conn = execution_info.get_source_config()
            tgt_conn = execution_info.get_target_config()
            rules_list = execution_info.get_rules_list()
            cmp_type = execution_info.get_comparison_type()

            # 4. 检查是否有表规则可执行
            if not rules_list:
                if progress_callback:
                    progress_callback("警告: 任务 {task_id} 没有找到可执行的表规则")
                return False

            # 记录执行信息摘要
            total_stats = execution_info.get_stats()
            self.sqlalchemy_service.update_task_progress(task_id, 1.0, "解析任务配置")

            # 5. 执行比对任务
            reporter = None
            source_conn = None
            target_conn = None

            try:
                # 5.1 创建数据库连接器（先创建空查询，后续会通过set_query设置具体查询）
                self.sqlalchemy_service.update_task_progress(task_id, 2.0, "创建数据库连接")
                source_conn = DB2Connector(src_conn, batch_size=self.batch_size)
                target_conn = DB2Connector(tgt_conn, batch_size=self.batch_size)

                # 5.2 创建Reporter
                self.sqlalchemy_service.update_task_progress(task_id, 3.0, "初始化报告器")
                reporter = ReporterFactory.create_reporters(self.reporter_config, task_id)
                if reporter:
                    reporter.open()

                # 5.4 执行多表比对任务
                self.sqlalchemy_service.update_task_progress(task_id, 5.0, "执行比对任务")
                total_tables = len(rules_list)

                for i, rule in enumerate(rules_list):
                    table_name = rule.get('table_name', rule.get('table_id', f'table_{i}'))
                    logger.info(f"执行表规则 {i+1}/{total_tables}: {table_name}")

                    # 计算当前表的进度范围 (5%-100%，为每个表分配95%的进度空间)
                    table_start_progress = 5.0 + (i * 95.0 / total_tables)
                    table_end_progress = 5.0 + ((i + 1) * 95.0 / total_tables)
                    self.sqlalchemy_service.update_task_progress(task_id, table_start_progress, table_name)

                    # 设置连接器查询
                    source_query = rule.get('source_sql', '')
                    target_query = rule.get('target_sql', '')
                    if not source_query or not target_query:
                        continue

                    source_conn.set_table_query(table_name, source_query)
                    target_conn.set_table_query(table_name, target_query)

                    if reporter and hasattr(reporter, 'set_sqlalchemy_service'):
                        reporter.set_sqlalchemy_service(self.sqlalchemy_service, task_id, table_name)

                    if cmp_type == 1:
                        # 流式归并算法：优先使用Cython优化版本
                        if self.use_cython:
                            table_stats = compare_sources_hybrid(source_conn, target_conn, reporter)
                        else:
                            table_stats = compare_sources(source_conn, target_conn, reporter)
                    else:
                        # 内存字典算法：优先使用Cython优化版本
                        if self.use_cython:
                            table_stats = compare_sources_memory_dict_hybrid(source_conn, target_conn, reporter)
                        else:
                            table_stats = compare_sources_memory_dict(source_conn, target_conn, reporter)

                    # 累计统计信息
                    for key in total_stats:
                        if key in table_stats:
                            total_stats[key] += table_stats[key]

                    # 同时更新进度、状态和统计信息
                    self.sqlalchemy_service.update_task_progress(task_id, table_end_progress, "比对完成",
                        status=TaskStatus.RUNNING, **table_stats)

            finally:
                # 6. 更新任务完成状态
                total_exec_time = time.perf_counter() - task_start_time
                self.sqlalchemy_service.update_task_progress(task_id, 100.0, "任务完成",
                    status=TaskStatus.COMPLETED, **total_stats)
                logger.info(f"任务执行完成: {task_id}, 总耗时: {total_exec_time:.2f}秒")

                try:
                    # 关闭连接器
                    source_conn.close()
                    target_conn.close()

                    # 关闭Reporter
                    if reporter:
                        reporter.close()
                except Exception as e:
                    logger.error(f"清理资源失败: {e}")

            return True

        except Exception as e:
            logger.error(f"任务执行失败: {task_id}, 错误: {e}", exc_info=True)
            self.sqlalchemy_service.update_task_status(task_id, TaskStatus.FAILED, error_msg=str(e))
            return False

    def migrate_config_to_database(self, config_manager: SmartConfigManager, user_name: str = "system") -> bool:
        """将配置文件迁移到数据库"""

        try:
            # 1. 迁移数据库连接配置
            db1_config = dict(config_manager.config['DB1'])
            db2_config = dict(config_manager.config['DB2'])

            # 迁移源连接
            source_conn = self.sqlalchemy_service.get_connection_by_name(db1_config.get('ip', ''))
            if source_conn:
                source_conn_id = source_conn.id
            else:
                source_conn_id = self.sqlalchemy_service.create_database_connection(
                    name=db1_config.get('ip', ''),
                    type=db1_config.get('type', 'DB2'),
                    host=db1_config.get('ip', ''),
                    port=int(db1_config.get('port', 50000)),
                    username=db1_config.get('user_name', ''),
                    password=db1_config.get('password', ''),
                    database=db1_config.get('schema', '')
                )

            # 迁移目标连接
            target_conn = self.sqlalchemy_service.get_connection_by_name(db2_config.get('ip', ''))
            if target_conn:
                target_conn_id = target_conn.id
            else:
                target_conn_id = self.sqlalchemy_service.create_database_connection(
                    name=db2_config.get('ip', ''),
                    type=db2_config.get('type', 'DB2'),
                    host=db2_config.get('ip', ''),
                    port=int(db2_config.get('port', 50000)),
                    username=db2_config.get('user_name', ''),
                    password=db2_config.get('password', ''),
                    database=db2_config.get('schema', '')
                )

            # 2. 创建比对模型
            common_config = dict(config_manager.config['COMMON'])
            model_id = self._create_model_from_config(user_name, source_conn_id, target_conn_id, common_config)

            # 3. 迁移表规则
            if config_manager.rules is not None:
                self._migrate_table_rules(model_id, config_manager.rules)

            return True

        except Exception as e:
            logger.error(f"配置迁移失败: {e}")
            return False

    def _create_model_from_config(self, user_name: str, source_conn_id: int, target_conn_id: int, common_config: Dict[str, str]) -> int:
        """从配置创建比对模型"""
        try:
            # 使用TAB_RULE配置的文件名称作为模型名称
            tab_rule_path = common_config.get('tab_rule', common_config.get('TAB_RULE', '从配置文件迁移的模型'))

            # 从路径中提取文件名
            if tab_rule_path != '从配置文件迁移的模型':
                file_name = os.path.basename(tab_rule_path)
                model_name = os.path.splitext(file_name)[0]
            else:
                model_name = tab_rule_path

            # 检查是否已存在相同名称的模型
            existing_model = self.sqlalchemy_service.get_comparison_model_by_name(model_name)
            if existing_model:
                return existing_model.id

            description = f"从配置文件迁移的比对模型"

            # 处理比对类型
            cmp_type_raw = common_config.get('cmp_type', common_config.get('CMP_TYPE', '2'))
            cmp_type = self._convert_cmp_type(cmp_type_raw)

            # 全局配置默认设为空
            global_config = {}

            # 创建比对模型
            model_id = self.sqlalchemy_service.create_comparison_model(
                name=model_name,
                description=description,
                source_connid=source_conn_id,
                target_connid=target_conn_id,
                cmp_type=cmp_type,
                global_config=global_config
            )

            return model_id

        except Exception as e:
            logger.error(f"从配置创建比对模型失败: {e}", exc_info=True)
            raise

    def _convert_cmp_type(self, cmp_type_raw: str) -> str:
        """转换比对类型"""
        # 将配置文件中的数字类型转换为字符串类型
        type_mapping = {
            '1': 'stream_merge',      # 流式归并算法
            '2': 'memory_dict',       # 内存字典算法
            'stream_merge': 'stream_merge',
            'memory_dict': 'memory_dict',
            'content': 'content'
        }

        return type_mapping.get(str(cmp_type_raw).lower(), 'content')

    def _migrate_table_rules(self, model_id: int, rules_xml) -> int:
        """迁移表规则到数据库"""
        count = 0

        try:
            for table_elem in rules_xml.findall('table'):
                try:
                    # 提取表规则信息
                    table_name = table_elem.get('name', table_elem.get('table_id', ''))
                    remark = table_elem.get('remark', '')

                    # 提取SQL语句
                    sql1_elem = table_elem.find('sql_1')
                    sql2_elem = table_elem.find('sql_2')

                    if sql1_elem is not None and sql2_elem is not None:
                        sql_1 = sql1_elem.text.strip() if sql1_elem.text else ""
                        sql_2 = sql2_elem.text.strip() if sql2_elem.text else ""
                        if not sql_1 or not sql_2:
                            continue

                        # 提取其他配置信息
                        primary_keys = []
                        ignore_fields = []
                        field_mappings = {}

                        # 解析主键配置
                        pk_elem = table_elem.find('primary_keys')
                        if pk_elem is not None and pk_elem.text:
                            primary_keys = [key.strip() for key in pk_elem.text.split(',') if key.strip()]

                        # 解析忽略字段配置
                        ignore_elem = table_elem.find('ignore_fields')
                        if ignore_elem is not None and ignore_elem.text:
                            ignore_fields = [field.strip() for field in ignore_elem.text.split(',') if field.strip()]

                        # 解析字段映射配置
                        mapping_elem = table_elem.find('field_mappings')
                        if mapping_elem is not None:
                            for mapping in mapping_elem.findall('mapping'):
                                source_field = mapping.get('source')
                                target_field = mapping.get('target')
                                if source_field and target_field:
                                    field_mappings[source_field] = target_field

                        # 创建表规则
                        self.sqlalchemy_service.create_table_rule(
                            model_id=model_id,
                            table_name=table_name,
                            sql_1=sql_1,
                            sql_2=sql_2,
                            remark=remark,
                            primary_keys=primary_keys,
                            ignore_fields=ignore_fields,
                            field_mappings=field_mappings
                        )

                        count += 1

                    else:
                        logger.warning(f"跳过缺少SQL的表规则: {table_name}")

                except Exception as e:
                    logger.error(f"迁移单个表规则失败: {table_elem.get('id', 'unknown')}, 错误: {e}")
                    continue

            logger.info(f"表规则迁移完成，成功处理 {count} 个规则")
            return count

        except Exception as e:
            logger.error(f"表规则迁移失败: {e}", exc_info=True)
            raise


def main(config_yaml: str):
    """主函数"""
    # 提取报告器配置
    with open(config_yaml, 'r', encoding='utf-8') as f:
        run_config = yaml.safe_load(f)
    report_config = run_config.get('reporters', {})

    # 根据配置执行任务
    task_config = run_config.get('task', {})
    model_id = task_config.get('model_id', None)
    task_type = task_config.get('type', 'from_config')
    user_name = task_config.get('user_name', 'yaml_user')
    
    # 创建并加载配置管理器
    config_manager = SmartConfigManager()
    config_inifile = config_manager.auto_discover_configs()
    config_manager.load_config(config_inifile[0])

    # 初始化任务编排器
    engine_config = run_config.get('engine', {})
    use_cython = engine_config.get('use_cython', True)  # 默认启用Cython
    orchestrator = ComparisonTaskOrchestrator(report_config=report_config, use_cython=use_cython)
    # 迁移配置到数据库
    if run_config.get('migrate_config', False):
        orchestrator.migrate_config_to_database(config_manager, user_name=user_name)

    try:
        if task_type == 'from_config':
            task_id = orchestrator.create_task_from_config(config_manager, user_name = user_name)
            result = orchestrator.execute_task_from_model(task_id=task_id)

        elif task_type == 'from_model':
            task_id = orchestrator.create_task_from_model(model_id = model_id, user_name = user_name)
            result = orchestrator.execute_task_from_model(task_id=task_id)
        else:
            raise ValueError(f"不支持的任务类型: {task_type}")

        logger.info(f"任务 {task_id} 执行完成。结果: {result}")

    except Exception as e:
        logger.error(f"任务执行失败: {e}", exc_info=True)
        sys.exit(1)


if __name__ == '__main__':
    # 自动发现YAML配置文件
    try:
        config_yaml = SmartConfigManager.auto_discover_configs('.yaml')
        if config_yaml and os.path.isfile(config_yaml[0]):
            main(config_yaml[0])      
    except Exception as e:
        logger.critical(f"主程序启动失败: {e}", exc_info=True)
        sys.exit(1)
