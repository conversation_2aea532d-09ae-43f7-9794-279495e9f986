# reporters/base_reporter.py
import logging
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
from core.models import DiffResult

logger = logging.getLogger(__name__)

class BaseReporter(ABC):
    """所有差异报告器的抽象基类。"""

    def __init__(self, config=None):
        """
        基础报告器初始化
        """
        self.config = config or {}
        self.diffs_list = []

        # SQLAlchemy服务实例
        self._sqlalchemy_service: Optional[Any] = None
        self._task_id: Optional[str] = None
        # 缓存管理配置
        self.cache_threshold = config.get('cache_threshold', 10000)

    @abstractmethod
    def open(self):
        """打开报告目标（例如，文件）。"""
        pass

    @abstractmethod
    def close(self):
        """关闭报告目标。"""
        pass

    def set_sqlalchemy_service(self, service: Any, task_id: str, table_name: str = None):
        """
        设置SQLAlchemy服务实例

        Args:
            service: SQLAlchemyComparisonService实例
            task_id: 任务ID
            table_name: 设置当前比对表名
        """
        self._sqlalchemy_service = service
        self._task_id = task_id
        self.table_name = table_name or 'unknown_table'

    def add_mismatch(self, diff_dict: Dict[str, Any]):
        """
        添加差异数据到内存收集器，支持增量持久化
        """
        self.diffs_list.append(diff_dict)

        # 检查是否需要触发增量持久化
        self._check_and_flush_cache()

    def report_diff(self, diff_result: Any, table_name: str = None):
        if isinstance(diff_result, dict):
            if 'table_name' not in diff_result:
                diff_result['table_name'] = table_name or self.table_name
            if 'task_id' not in diff_result:
                diff_result['task_id'] = self._task_id or ''
            self.add_mismatch(diff_result)
        else:
            # DiffResult对象，需要转换
            if isinstance(diff_result, DiffResult):
                if table_name is None:
                    raise ValueError("当传入DiffResult对象时，table_name参数不能为空")

                diff_dict = {
                    'task_id': getattr(self, '_task_id', ''),
                    'table_name': table_name,
                    'record_key': str(diff_result.key),
                    'status': diff_result.status,
                    'field_name': None,
                    'source_value': diff_result.value_a,
                    'target_value': diff_result.value_b
                }
                self.add_mismatch(diff_dict)
            else:
                raise TypeError(f"不支持的数据类型: {type(diff_result)}")

    def report_diff_legacy(self, diff_result: DiffResult, table_name: str):
        # 调用新的统一接口
        self.report_diff(diff_result, table_name)

    def _check_and_flush_cache(self):
        # 检查记录数量阈值
        if len(self.diffs_list) >= self.cache_threshold:
            logger.debug(f"达到缓存数量阈值: {len(self.diffs_list)}/{self.cache_threshold}")
            self._flush_cache()

    def _flush_cache(self):
        """
        刷新缓存到持久化存储
        """
        if not self.diffs_list:
            return

        if self._sqlalchemy_service and self._task_id:
            # 复制当前缓存数据并立即清空，避免重复处理
            diffs_to_write = self.diffs_list.copy()
            self.diffs_list.clear()

            try:
                # 直接调用SQLAlchemy服务的批量写入方法
                result = self._sqlalchemy_service.batch_insert_comparison_results(diffs_to_write)
                logger.debug(f"增量持久化完成: {result}")

            except Exception as e:
                logger.error(f"增量持久化失败: {e}")
                # 失败时将数据重新放回缓存
                self.diffs_list = diffs_to_write + self.diffs_list
                raise
        else:
            logger.warning("未设置SQLAlchemy服务或任务ID，跳过增量持久化")

    def force_flush(self):
        """
        强制刷新所有缓存数据
        """
        if self.diffs_list:
            logger.info(f"强制刷新缓存: {len(self.diffs_list)} 条记录")
            self._flush_cache()

    def get_diffs(self, clear_cache: bool = False) -> List[Dict[str, Any]]:
        """
        返回收集到的所有差异数据

        Args:
            clear_cache: 是否清空缓存

        Returns:
            差异数据列表
        """
        if clear_cache:
            diffs = self.diffs_list.copy()
            self.diffs_list.clear()
            return diffs
        else:
            return self.diffs_list.copy()

    def get_cache_stats(self) -> Dict[str, Any]:
        """
        获取缓存统计信息

        Returns:
            缓存统计信息
        """
        return {
            'current_cache_size': len(self.diffs_list),
            'cache_threshold': self.cache_threshold,
            'has_sqlalchemy_service': self._sqlalchemy_service is not None
        }

    def __enter__(self):
        self.open()
        return self

    def __exit__(self, _exc_type, _exc_val, _exc_tb):
        """退出上下文管理器"""
        self.close()
        return False