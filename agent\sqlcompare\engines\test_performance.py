"""
混合引擎性能测试脚本
==================

用于测试和验证混合引擎的性能和功能。
"""

import sys
import os
import time
import logging
from typing import Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_engine_detection():
    """测试引擎检测功能"""
    logger.info("=== 测试引擎检测功能 ===")
    
    try:
        from engines.detector import get_engine_info
        
        engine_info = get_engine_info()
        
        print(f"Cython引擎可用: {engine_info['cython_available']}")
        print(f"Python引擎可用: {engine_info['python_available']}")
        print(f"推荐引擎: {engine_info['recommended_engine']}")
        print(f"Cython性能提升: {engine_info['cython_performance_boost']}%")
        
        return True
        
    except Exception as e:
        logger.error(f"引擎检测测试失败: {e}")
        return False


def test_engine_selection():
    """测试引擎选择功能"""
    logger.info("=== 测试引擎选择功能 ===")
    
    try:
        from engines.selector import EngineSelector, AlgorithmType
        
        selector = EngineSelector()
        
        # 测试不同场景的引擎选择
        test_cases = [
            {
                'name': '小数据集流式归并',
                'algorithm': AlgorithmType.MERGE_SORT,
                'records_a': 1000,
                'records_b': 1000
            },
            {
                'name': '大数据集内存字典',
                'algorithm': AlgorithmType.MEMORY_DICT,
                'records_a': 1000000,
                'records_b': 1000000
            },
            {
                'name': '超大数据集流式归并',
                'algorithm': AlgorithmType.MERGE_SORT,
                'records_a': 100000000,
                'records_b': 100000000
            }
        ]
        
        for case in test_cases:
            selected_engine, details = selector.select_optimal_engine(
                algorithm_type=case['algorithm'],
                estimated_records_a=case['records_a'],
                estimated_records_b=case['records_b']
            )
            
            print(f"\n{case['name']}:")
            print(f"  选择引擎: {selected_engine.value}")
            print(f"  选择原因: {details['selection_reason']}")
            print(f"  预期性能提升: {details['expected_performance_boost']}%")
        
        return True
        
    except Exception as e:
        logger.error(f"引擎选择测试失败: {e}")
        return False


def test_performance_monitoring():
    """测试性能监控功能"""
    logger.info("=== 测试性能监控功能 ===")
    
    try:
        from engines.monitor import PerformanceMonitor
        from engines.selector import EngineType, AlgorithmType
        
        monitor = PerformanceMonitor()
        
        # 模拟监控会话
        execution_id = "test_exec_001"
        
        # 开始监控
        metrics = monitor.start_monitoring(
            execution_id=execution_id,
            engine_type=EngineType.PYTHON,
            algorithm_type=AlgorithmType.MERGE_SORT
        )
        
        print(f"开始监控: {execution_id}")
        
        # 模拟处理过程
        time.sleep(0.1)
        monitor.update_metrics(
            execution_id=execution_id,
            records_processed=10000,
            memory_usage_mb=50.0,
            cpu_usage_percent=25.0
        )
        
        time.sleep(0.1)
        monitor.update_metrics(
            execution_id=execution_id,
            records_processed=20000,
            memory_usage_mb=75.0,
            cpu_usage_percent=30.0
        )
        
        # 停止监控
        completed_metrics = monitor.stop_monitoring(execution_id)
        
        print(f"监控完成:")
        print(f"  执行时间: {completed_metrics.duration:.3f}秒")
        print(f"  处理记录: {completed_metrics.records_processed}")
        print(f"  处理速率: {completed_metrics.records_per_second:.0f} 条/秒")
        print(f"  内存使用: {completed_metrics.memory_usage_mb:.1f} MB")
        print(f"  CPU使用: {completed_metrics.cpu_usage_percent:.1f}%")
        
        # 获取性能摘要
        summary = monitor.get_performance_summary()
        print(f"\n性能摘要:")
        print(f"  总执行次数: {summary['total_executions']}")
        print(f"  成功率: {summary['success_rate']:.1%}")
        print(f"  平均执行时间: {summary['average_duration']:.3f}秒")
        print(f"  平均处理速率: {summary['average_records_per_second']:.0f} 条/秒")
        
        return True
        
    except Exception as e:
        logger.error(f"性能监控测试失败: {e}")
        return False


def test_benchmark():
    """测试基准测试功能"""
    logger.info("=== 测试基准测试功能 ===")
    
    try:
        from engines.interface import run_performance_benchmark
        
        # 运行基准测试
        results = run_performance_benchmark(
            engines=['python'],  # 只测试可用的引擎
            algorithms=['merge_sort', 'memory_dict'],
            data_sizes=[1000, 10000, 100000]
        )
        
        print(f"基准测试ID: {results['benchmark_id']}")
        print(f"测试配置数量: {len(results['results'])}")
        
        # 显示部分结果
        for config_key, result in list(results['results'].items())[:3]:
            print(f"\n配置: {config_key}")
            print(f"  引擎: {result['engine_type']}")
            print(f"  算法: {result['algorithm_type']}")
            print(f"  数据量: {result['data_size']:,}")
            print(f"  处理速率: {result['records_per_second']:.0f} 条/秒")
            print(f"  预估时间: {result['estimated_duration']:.3f}秒")
        
        # 显示摘要
        summary = results['summary']
        print(f"\n基准测试摘要:")
        print(f"  最佳引擎: {summary['best_engine']}")
        print(f"  最佳算法: {summary['best_algorithm']}")
        
        return True
        
    except Exception as e:
        logger.error(f"基准测试失败: {e}")
        return False


def test_hybrid_engine_interface():
    """测试混合引擎接口"""
    logger.info("=== 测试混合引擎接口 ===")
    
    try:
        from engines.interface import get_hybrid_engine_statistics, reset_hybrid_engine
        
        # 获取统计信息
        stats = get_hybrid_engine_statistics()
        
        print("混合引擎统计信息:")
        print(f"  引擎信息: {stats['engine_info']['recommended_engine']}")
        print(f"  可用算法: {len(stats['available_algorithms'])}")
        
        for algorithm in stats['available_algorithms']:
            print(f"    - {algorithm['name']}: {algorithm['description']}")
        
        # 重置引擎状态
        reset_hybrid_engine()
        print("引擎状态已重置")
        
        return True
        
    except Exception as e:
        logger.error(f"混合引擎接口测试失败: {e}")
        return False


def main():
    """主测试函数"""
    logger.info("开始混合引擎性能测试")
    
    test_results = {
        '引擎检测': test_engine_detection(),
        '引擎选择': test_engine_selection(),
        '性能监控': test_performance_monitoring(),
        '基准测试': test_benchmark(),
        '混合引擎接口': test_hybrid_engine_interface()
    }
    
    print("\n" + "="*50)
    print("测试结果汇总:")
    print("="*50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！混合引擎模块功能正常。")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
        return False


if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)
