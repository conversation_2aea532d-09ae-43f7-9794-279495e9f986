"""
混合引擎兼容性模块
================

为了保持与现有v4.0架构的兼容性，这个模块提供了向后兼容的接口。
实际的混合引擎实现位于engines/目录下。
"""

import logging
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

# 导入新的混合引擎接口
try:
    from engines.interface import (
        compare_sources_hybrid as _compare_sources_hybrid,
        compare_sources_memory_dict_hybrid as _compare_sources_memory_dict_hybrid,
        get_hybrid_engine_statistics
    )
    from engines.detector import get_engine_info as _get_engine_info

    _ENGINES_AVAILABLE = True
    logger.info("混合引擎模块加载成功")

except ImportError as e:
    logger.warning(f"混合引擎模块导入失败: {e}")
    _ENGINES_AVAILABLE = False


def get_engine_info() -> Dict[str, Any]:
    """
    获取引擎信息

    Returns:
        Dict: 引擎信息字典
    """
    if _ENGINES_AVAILABLE:
        return _get_engine_info()
    else:
        # 降级处理：返回基本的引擎信息
        logger.warning("使用降级的引擎信息检测")

        # 检测Cython引擎
        cython_available = False
        try:
            from core.engine_cython import compare_sources_cython
            cython_available = True
        except ImportError:
            pass

        # 检测Python引擎
        python_available = False
        try:
            from core.engine import compare_sources
            python_available = True
        except ImportError:
            pass

        return {
            'cython_available': cython_available,
            'python_available': python_available,
            'recommended_engine': 'cython' if cython_available else 'python' if python_available else None,
            'cython_performance_boost': 60 if cython_available else 0,
            'system_info': {},
            'detailed_info': {
                'engines': {
                    'cython': {'available': cython_available},
                    'python': {'available': python_available}
                }
            }
        }


def compare_sources_hybrid(source_a, source_b, reporter) -> Dict[str, Any]:
    """
    混合引擎流式归并比对函数（兼容性接口）

    Args:
        source_a: 数据源A连接器
        source_b: 数据源B连接器
        reporter: 差异报告器

    Returns:
        Dict: 比对结果统计信息
    """
    if _ENGINES_AVAILABLE:
        return _compare_sources_hybrid(source_a, source_b, reporter)
    else:
        # 降级处理：直接使用可用的引擎
        logger.warning("混合引擎不可用，使用降级处理")
        return _fallback_compare_sources(source_a, source_b, reporter, algorithm_type=1)


def compare_sources_memory_dict_hybrid(source_a, source_b, reporter) -> Dict[str, Any]:
    """
    混合引擎内存字典比对函数（兼容性接口）

    Args:
        source_a: 数据源A连接器
        source_b: 数据源B连接器
        reporter: 差异报告器

    Returns:
        Dict: 比对结果统计信息
    """
    if _ENGINES_AVAILABLE:
        return _compare_sources_memory_dict_hybrid(source_a, source_b, reporter)
    else:
        # 降级处理：直接使用可用的引擎
        logger.warning("混合引擎不可用，使用降级处理")
        return _fallback_compare_sources(source_a, source_b, reporter, algorithm_type=2)


def _fallback_compare_sources(source_a, source_b, reporter, algorithm_type: int) -> Dict[str, Any]:
    """
    降级比对函数

    当混合引擎不可用时，直接使用可用的引擎进行比对。

    Args:
        source_a: 数据源A连接器
        source_b: 数据源B连接器
        reporter: 差异报告器
        algorithm_type: 算法类型 (1=流式归并, 2=内存字典)

    Returns:
        Dict: 比对结果统计信息
    """
    logger.info(f"执行降级比对，算法类型: {algorithm_type}")

    # 优先尝试Cython引擎
    try:
        if algorithm_type == 1:
            from core.engine_cython import compare_sources_cython
            logger.info("使用Cython流式归并算法（降级模式）")
            return compare_sources_cython(source_a, source_b, reporter)
        elif algorithm_type == 2:
            from core.engine_cython import compare_sources_memory_dict_cython
            logger.info("使用Cython内存字典算法（降级模式）")
            return compare_sources_memory_dict_cython(source_a, source_b, reporter)
    except ImportError:
        logger.info("Cython引擎不可用，尝试Python引擎")

    # 降级到Python引擎
    try:
        if algorithm_type == 1:
            from core.engine import compare_sources
            logger.info("使用Python流式归并算法（降级模式）")
            return compare_sources(source_a, source_b, reporter)
        elif algorithm_type == 2:
            from core.engine import compare_sources_memory_dict
            logger.info("使用Python内存字典算法（降级模式）")
            return compare_sources_memory_dict(source_a, source_b, reporter)
    except ImportError:
        logger.error("Python引擎也不可用")
        raise RuntimeError("没有可用的比对引擎")

    raise ValueError(f"不支持的算法类型: {algorithm_type}")


# 为了完全兼容，提供额外的便捷函数
def get_hybrid_engine_status() -> Dict[str, Any]:
    """
    获取混合引擎状态信息

    Returns:
        Dict: 状态信息
    """
    engine_info = get_engine_info()

    status = {
        'hybrid_engine_available': _ENGINES_AVAILABLE,
        'cython_available': engine_info.get('cython_available', False),
        'python_available': engine_info.get('python_available', False),
        'recommended_engine': engine_info.get('recommended_engine'),
        'fallback_mode': not _ENGINES_AVAILABLE
    }

    if _ENGINES_AVAILABLE:
        try:
            stats = get_hybrid_engine_statistics()
            status['execution_statistics'] = stats.get('execution_statistics', {})
        except Exception as e:
            logger.debug(f"获取混合引擎统计信息失败: {e}")

    return status