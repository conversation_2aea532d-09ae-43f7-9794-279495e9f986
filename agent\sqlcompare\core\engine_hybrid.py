"""
SQLCompare 混合比对引擎模块
========================

提供智能引擎选择和统一的比对接口，支持：
- 高性能Cython引擎（优先选择）
- 标准Python引擎（fallback）
- 自动引擎检测和降级
- 统一的API接口
"""

import logging
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)


def get_engine_info() -> Dict[str, Any]:
    """
    获取引擎信息

    Returns:
        Dict: 引擎信息字典
    """
    # 检测Cython引擎
    cython_available = False
    cython_error = None
    try:
        from core.engine_cython import compare_sources_cython
        cython_available = True
        logger.debug("Cython引擎检测成功")
    except ImportError as e:
        cython_error = str(e)
        logger.debug(f"Cython引擎不可用: {e}")

    # 检测Python引擎
    python_available = False
    python_error = None
    try:
        from core.engine import compare_sources
        python_available = True
        logger.debug("Python引擎检测成功")
    except ImportError as e:
        python_error = str(e)
        logger.debug(f"Python引擎不可用: {e}")

    # 确定推荐引擎
    if cython_available:
        recommended_engine = 'cython'
    elif python_available:
        recommended_engine = 'python'
    else:
        recommended_engine = None

    return {
        'cython_available': cython_available,
        'python_available': python_available,
        'recommended_engine': recommended_engine,
        'cython_performance_boost': 60 if cython_available else 0,
        'cython_error': cython_error,
        'python_error': python_error
    }


def compare_sources_hybrid(source_a, source_b, reporter) -> Dict[str, Any]:
    """
    混合引擎流式归并比对函数

    自动选择最优引擎执行流式归并算法：
    1. 优先使用Cython引擎（性能提升50-70%）
    2. 如果Cython不可用，降级到Python引擎

    Args:
        source_a: 数据源A连接器
        source_b: 数据源B连接器
        reporter: 差异报告器

    Returns:
        Dict: 比对结果统计信息
    """
    logger.info("开始混合引擎流式归并比对")

    # 优先尝试Cython引擎
    try:
        from core.engine_cython import compare_sources_cython
        logger.info("✅ 使用Cython流式归并算法，预期性能提升50-70%")
        return compare_sources_cython(source_a, source_b, reporter)
    except ImportError:
        logger.info("Cython引擎不可用，降级到Python引擎")

    # 降级到Python引擎
    try:
        from core.engine import compare_sources
        logger.info("📝 使用Python流式归并算法")
        return compare_sources(source_a, source_b, reporter)
    except ImportError:
        logger.error("没有可用的比对引擎")
        raise RuntimeError("没有可用的比对引擎")


def compare_sources_memory_hybrid(source_a, source_b, reporter) -> Dict[str, Any]:
    """
    混合引擎内存字典比对函数

    自动选择最优引擎执行内存字典算法：
    1. 优先使用Cython引擎（性能提升50-70%）
    2. 如果Cython不可用，降级到Python引擎

    Args:
        source_a: 数据源A连接器
        source_b: 数据源B连接器
        reporter: 差异报告器

    Returns:
        Dict: 比对结果统计信息
    """
    logger.info("开始混合引擎内存字典比对")

    # 优先尝试Cython引擎
    try:
        from core.engine_cython import compare_sources_memory_dict_cython
        logger.info("✅ 使用Cython内存字典算法，预期性能提升50-70%")
        return compare_sources_memory_dict_cython(source_a, source_b, reporter)
    except ImportError:
        logger.info("Cython引擎不可用，降级到Python引擎")

    # 降级到Python引擎
    try:
        from core.engine import compare_sources_memory
        logger.info("📝 使用Python内存字典算法")
        return compare_sources_memory(source_a, source_b, reporter)
    except ImportError:
        logger.error("没有可用的比对引擎")
        raise RuntimeError("没有可用的比对引擎")


def get_hybrid_engine_status() -> Dict[str, Any]:
    """
    获取混合引擎状态信息

    Returns:
        Dict: 状态信息
    """
    engine_info = get_engine_info()

    return {
        'cython_available': engine_info.get('cython_available', False),
        'python_available': engine_info.get('python_available', False),
        'recommended_engine': engine_info.get('recommended_engine'),
        'cython_performance_boost': engine_info.get('cython_performance_boost', 0),
        'status': 'ready' if engine_info.get('recommended_engine') else 'no_engines'
    }