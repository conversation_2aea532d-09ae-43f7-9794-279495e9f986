# reporters/gui_reporter.py
"""
GUI集成报告器 - 专为GUI界面设计的比对结果报告器
支持实时结果更新和进度回调
"""
import time
from typing import Dict, Any, Callable, Optional, List
from reporters.base_reporter import BaseReporter
from core.models import DiffResult


class GuiReporter(BaseReporter):
    """GUI集成报告器，支持实时结果更新和进度回调"""

    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化GUI报告器
        
        Args:
            config: 配置字典，包含以下可选项：
                - progress_callback: 进度回调函数
                - result_callback: 结果回调函数
                - update_interval: 更新间隔（秒）
        """
        self.config = config or {}
        
        # 回调函数
        self.progress_callback: Optional[Callable] = self.config.get('progress_callback')
        self.result_callback: Optional[Callable] = self.config.get('result_callback')
        
        # 更新控制
        self.update_interval = self.config.get('update_interval', 10.0)  # 10秒更新一次
        self.last_update_time = 0
        
        # 统计信息
        self.total_processed = 0
        self.total_differences = 0
        self.start_time = None
        
        # 结果缓存
        self.results_buffer: List[DiffResult] = []
        self.buffer_size = self.config.get('buffer_size', 1000)
        
        # 状态标志
        self.is_open = False

    def open(self):
        """打开报告器"""
        self.is_open = True
        self.start_time = time.time()
        self.total_processed = 0
        self.total_differences = 0
        self.results_buffer.clear()
        self.last_update_time = 0
        
        if self.progress_callback:
            self.progress_callback({
                'status': 'started',
                'message': '开始比对任务',
                'processed': 0,
                'differences': 0,
                'speed': 0
            })

    def close(self):
        """关闭报告器"""
        if not self.is_open:
            return
            
        self.is_open = False
        
        # 发送剩余的结果
        if self.results_buffer and self.result_callback:
            self.result_callback(self.results_buffer.copy())
            self.results_buffer.clear()
        
        # 发送最终统计
        if self.progress_callback:
            total_time = time.time() - self.start_time if self.start_time else 0
            final_speed = self.total_processed / total_time if total_time > 0 else 0
            
            self.progress_callback({
                'status': 'completed',
                'message': '比对任务完成',
                'processed': self.total_processed,
                'differences': self.total_differences,
                'speed': final_speed,
                'total_time': total_time
            })

    def report_diff(self, diff_result: Any, table_name: str = None):
        """报告差异结果"""
        if not self.is_open:
            raise RuntimeError("报告器未打开")

        # 处理输入数据格式
        if isinstance(diff_result, dict):
            diff_dict = DiffResult(
                key=diff_result.get('record_key', ''),
                status=diff_result.get('status', ''),
                value_a=diff_result.get('source_value'),
                value_b=diff_result.get('target_value')
            )
            table_name = diff_result.get('table_name', table_name or '')
        else:
            diff_dict = diff_result

        # 更新统计
        self.total_processed += 1
        self.total_differences += 1
        
        # 添加到缓冲区
        self.results_buffer.append(diff_dict)
        
        # 检查是否需要更新
        current_time = time.time()
        if (current_time - self.last_update_time >= self.update_interval or 
            len(self.results_buffer) >= self.buffer_size):
            self._send_updates(current_time)

    def report_match(self, key: str, value_a=None, value_b=None):
        """报告匹配记录或进度信息"""
        if not self.is_open:
            return

        # 检查是否是进度信息
        if key.startswith('progress_') or key.startswith('load_') or key.startswith('report_progress_'):
            # 这是进度信息，value_a是处理数量，value_b是差异数量
            if isinstance(value_a, (int, float)):
                self.total_processed = int(value_a)
            if isinstance(value_b, (int, float)):
                self.total_differences = int(value_b)

            # 强制发送进度更新
            current_time = time.time()
            self._send_progress_update(current_time)
        else:
            # 普通匹配记录
            self.total_processed += 1

            # 定期发送进度更新
            current_time = time.time()
            if current_time - self.last_update_time >= self.update_interval:
                self._send_progress_update(current_time)

    def _send_updates(self, current_time: float):
        """发送更新信息"""
        # 发送结果更新
        if self.results_buffer and self.result_callback:
            self.result_callback(self.results_buffer.copy())
            self.results_buffer.clear()
        
        # 发送进度更新
        self._send_progress_update(current_time)

    def _send_progress_update(self, current_time: float):
        """发送进度更新"""
        if not self.progress_callback:
            return
            
        # 计算处理速度
        elapsed_time = current_time - self.start_time if self.start_time else 0
        speed = self.total_processed / elapsed_time if elapsed_time > 0 else 0
        
        self.progress_callback({
            'status': 'running',
            'message': f'正在处理数据...',
            'processed': self.total_processed,
            'differences': self.total_differences,
            'speed': speed,
            'elapsed_time': elapsed_time
        })
        
        self.last_update_time = current_time

    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        elapsed_time = time.time() - self.start_time if self.start_time else 0
        speed = self.total_processed / elapsed_time if elapsed_time > 0 else 0
        
        return {
            'total_processed': self.total_processed,
            'total_differences': self.total_differences,
            'elapsed_time': elapsed_time,
            'speed': speed,
            'buffer_size': len(self.results_buffer)
        }

    def set_progress_callback(self, callback: Callable):
        """设置进度回调函数"""
        self.progress_callback = callback

    def set_result_callback(self, callback: Callable):
        """设置结果回调函数"""
        self.result_callback = callback

    def flush(self):
        """强制刷新缓冲区"""
        if self.results_buffer and self.result_callback:
            self.result_callback(self.results_buffer.copy())
            self.results_buffer.clear()
        
        if self.progress_callback:
            current_time = time.time()
            self._send_progress_update(current_time)


class GuiProgressReporter(GuiReporter):
    """专门用于进度报告的简化版本"""
    
    def __init__(self, progress_callback: Callable):
        super().__init__({'progress_callback': progress_callback})
        
    def report_diff(self, data, table_name: str = None):
        """
        只报告进度，不缓存结果
        支持两种输入格式：
        1. 字典格式（新接口，性能优化）
        2. DiffResult对象（旧接口，向后兼容）
        """
        if not self.is_open:
            return

        self.total_processed += 1
        self.total_differences += 1
        
        current_time = time.time()
        if current_time - self.last_update_time >= self.update_interval:
            self._send_progress_update(current_time)


class GuiResultReporter(GuiReporter):
    """专门用于结果收集的版本"""
    
    def __init__(self, result_callback: Callable, buffer_size: int = 100):
        super().__init__({
            'result_callback': result_callback,
            'buffer_size': buffer_size,
            'update_interval': 0.5  # 更频繁的结果更新
        })
