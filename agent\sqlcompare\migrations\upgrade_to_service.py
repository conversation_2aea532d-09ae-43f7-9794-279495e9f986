"""
数据库迁移脚本：从现有SQLite结构升级到服务化结构
"""
import sqlite3
import uuid
from datetime import datetime
from typing import Dict, Any, List
from pathlib import Path

class DatabaseMigration:
    """数据库迁移工具"""
    
    def __init__(self, old_db_path: str, new_db_path: str):
        self.old_db_path = old_db_path
        self.new_db_path = new_db_path
        self.migration_log = []
        
    def migrate(self, default_user_id: str = "system") -> bool:
        """执行完整迁移"""
        try:
            self._log("开始数据库迁移")
            
            # 1. 创建新数据库结构
            self._create_new_database()
            
            # 2. 创建默认用户
            self._create_default_user(default_user_id)
            
            # 3. 迁移现有数据
            if Path(self.old_db_path).exists():
                self._migrate_existing_data(default_user_id)
            else:
                self._log("未找到旧数据库文件，跳过数据迁移")
            
            # 4. 验证迁移结果
            self._verify_migration()
            
            self._log("数据库迁移完成")
            return True
            
        except Exception as e:
            self._log(f"迁移失败: {str(e)}")
            return False
    
    def _create_new_database(self):
        """创建新的数据库结构"""
        self._log("创建新数据库结构")
        
        conn = sqlite3.connect(self.new_db_path)
        cursor = conn.cursor()
        
        # 创建所有服务化表
        tables_sql = [
            # 用户表
            """
            CREATE TABLE IF NOT EXISTS user_agent (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id VARCHAR(50) UNIQUE NOT NULL,
                username VARCHAR(100) NOT NULL,
                email VARCHAR(255) UNIQUE,
                password VARCHAR(255) NOT NULL,
                role VARCHAR(20) DEFAULT 'user',
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_login_at TIMESTAMP
            );
            """,
            
            # 用户会话表
            """
            CREATE TABLE IF NOT EXISTS user_sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id VARCHAR(128) UNIQUE NOT NULL,
                user_id VARCHAR(50) NOT NULL,
                ip_address VARCHAR(45),
                user_agent TEXT,
                expires_at TIMESTAMP NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES user_agent(user_id)
            );
            """,
            
            # 数据库连接表
            """
            CREATE TABLE IF NOT EXISTS comparison_connections (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(100) NOT NULL,
                type VARCHAR(20) NOT NULL,
                host VARCHAR(255) NOT NULL,
                port INTEGER NOT NULL,
                username VARCHAR(100) NOT NULL,
                password VARCHAR(255) NOT NULL,
                database VARCHAR(100) NOT NULL,
                params JSON,
                status VARCHAR(20) DEFAULT 'active',
                create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            """,

            # 比对模型表
            """
            CREATE TABLE IF NOT EXISTS comparison_models (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(100) NOT NULL,
                description TEXT,
                source_connid INTEGER NOT NULL,
                target_connid INTEGER NOT NULL,
                cmp_type VARCHAR(20) DEFAULT 'content',
                global_config JSON,
                status BOOLEAN DEFAULT TRUE,
                create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (source_connid) REFERENCES comparison_connections(id),
                FOREIGN KEY (target_connid) REFERENCES comparison_connections(id)
            );
            """,

            # 表规则表
            """
            CREATE TABLE IF NOT EXISTS comparison_table_rules (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                model_id INTEGER NOT NULL,
                table_id VARCHAR(50) NOT NULL,
                table_name VARCHAR(255) NOT NULL,
                sql_1 TEXT NOT NULL,
                sql_2 TEXT NOT NULL,
                remark VARCHAR(500),
                primary_keys JSON,
                ignore_fields JSON,
                field_mappings JSON,
                is_active BOOLEAN DEFAULT TRUE,
                create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (model_id) REFERENCES comparison_models(id)
            );
            """,

            # 比对任务表
            """
            CREATE TABLE IF NOT EXISTS comparison_tasks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                task_id VARCHAR(50) NOT NULL,
                user_id VARCHAR(50) NOT NULL,
                model_id INTEGER NOT NULL,
                table_rule_id INTEGER NOT NULL,
                task_name VARCHAR(200),
                description TEXT,
                status VARCHAR(20) DEFAULT 'pending',
                progress_pct DECIMAL(5,2) DEFAULT 0.0,
                current_step VARCHAR(100),
                create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                start_time TIMESTAMP,
                complete_time TIMESTAMP,
                update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                total_records INTEGER DEFAULT 0,
                processed_records INTEGER DEFAULT 0,
                diff_records INTEGER DEFAULT 0,
                source_only INTEGER DEFAULT 0,
                target_only INTEGER DEFAULT 0,
                exec_time DECIMAL(10,3) DEFAULT 0,
                error_msg TEXT,
                retention_days INTEGER DEFAULT 30,
                auto_cleanup BOOLEAN DEFAULT TRUE,
                FOREIGN KEY (user_id) REFERENCES user_agent(user_id),
                FOREIGN KEY (model_id) REFERENCES comparison_models(id) ON DELETE CASCADE,
                FOREIGN KEY (table_rule_id) REFERENCES comparison_table_rules(id) ON DELETE CASCADE
            );
            """,
            
            # 比对结果表（修复：添加外键约束和缺少的字段）
            """
            CREATE TABLE IF NOT EXISTS comparison_results (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                task_id VARCHAR(50) NOT NULL,
                table_name VARCHAR(100) NOT NULL,
                record_key VARCHAR(128) NOT NULL,
                status VARCHAR(2) NOT NULL,
                field_name VARCHAR(100),
                source_value TEXT,
                target_value TEXT,
                diff_type VARCHAR(2),
                partition_key VARCHAR(50),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (task_id) REFERENCES comparison_tasks(task_id)
            );
            """,
            

            

            

            

            

            
            # API访问日志表
            """
            CREATE TABLE IF NOT EXISTS api_access_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                request_id VARCHAR(50) UNIQUE NOT NULL,
                user_id VARCHAR(50),
                endpoint VARCHAR(200) NOT NULL,
                method VARCHAR(10) NOT NULL,
                ip_address VARCHAR(45),
                user_agent TEXT,
                request_body TEXT,
                response_status INTEGER,
                response_time_ms INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            """
        ]
        
        # 执行表创建
        for sql in tables_sql:
            cursor.execute(sql)
        
        # 创建索引
        indexes_sql = [
            # 用户相关索引
            "CREATE INDEX IF NOT EXISTS idx_users_user_id ON user_agent(user_id);",
            "CREATE INDEX IF NOT EXISTS idx_users_email ON user_agent(email);",
            "CREATE INDEX IF NOT EXISTS idx_users_role ON user_agent(role);"
            "CREATE INDEX IF NOT EXISTS idx_sessions_session_id ON user_sessions(session_id);",
            "CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON user_sessions(user_id);",
            "CREATE INDEX IF NOT EXISTS idx_sessions_expires ON user_sessions(expires_at);",

            # 配置管理层索引
            "CREATE INDEX IF NOT EXISTS idx_connections_name ON comparison_connections(name);",
            "CREATE INDEX IF NOT EXISTS idx_connections_type ON comparison_connections(type);",
            "CREATE INDEX IF NOT EXISTS idx_connections_status ON comparison_connections(status);",
            "CREATE INDEX IF NOT EXISTS idx_models_name ON comparison_models(name);",
            "CREATE INDEX IF NOT EXISTS idx_models_status ON comparison_models(status);",
            "CREATE INDEX IF NOT EXISTS idx_models_source_conn ON comparison_models(source_connid);",
            "CREATE INDEX IF NOT EXISTS idx_models_target_conn ON comparison_models(target_connid);",
            "CREATE INDEX IF NOT EXISTS idx_rules_model_id ON comparison_table_rules(model_id);",
            "CREATE INDEX IF NOT EXISTS idx_rules_table_id ON comparison_table_rules(table_id);",
            "CREATE INDEX IF NOT EXISTS idx_rules_model_table ON comparison_table_rules(model_id, table_id);",
            "CREATE INDEX IF NOT EXISTS idx_rules_active ON comparison_table_rules(is_active);",

            # 执行管理层索引（修复：完整的索引定义）
            "CREATE INDEX IF NOT EXISTS idx_tasks_task_id ON comparison_tasks(task_id);",
            "CREATE INDEX IF NOT EXISTS idx_tasks_user_status ON comparison_tasks(user_id, status);",
            "CREATE INDEX IF NOT EXISTS idx_tasks_model_id ON comparison_tasks(model_id);",
            "CREATE INDEX IF NOT EXISTS idx_tasks_table_rule ON comparison_tasks(table_rule_id);",
            "CREATE INDEX IF NOT EXISTS idx_tasks_status ON comparison_tasks(status);",
            "CREATE INDEX IF NOT EXISTS idx_tasks_create_time ON comparison_tasks(create_time);",
            "CREATE INDEX IF NOT EXISTS idx_tasks_task_status ON comparison_tasks(task_id, status);",
            "CREATE INDEX IF NOT EXISTS idx_tasks_task_table ON comparison_tasks(task_id, table_rule_id);",
            "CREATE INDEX IF NOT EXISTS idx_tasks_model_status ON comparison_tasks(model_id, status);",
            "CREATE UNIQUE INDEX IF NOT EXISTS uq_task_table_rule ON comparison_tasks(task_id, table_rule_id);",

            # 结果相关索引（修复：完整的索引定义）
            "CREATE INDEX IF NOT EXISTS idx_results_task_id ON comparison_results(task_id);",
            "CREATE INDEX IF NOT EXISTS idx_results_table_name ON comparison_results(table_name);",
            "CREATE INDEX IF NOT EXISTS idx_results_status ON comparison_results(status);",
            "CREATE INDEX IF NOT EXISTS idx_results_table_status ON comparison_results(table_name, status);",
            "CREATE INDEX IF NOT EXISTS idx_results_task_status ON comparison_results(task_id, status);",
            "CREATE INDEX IF NOT EXISTS idx_results_task_table ON comparison_results(task_id, table_name);",
            "CREATE INDEX IF NOT EXISTS idx_results_partition ON comparison_results(partition_key);",
            "CREATE INDEX IF NOT EXISTS idx_results_created_at ON comparison_results(created_at);",

            # 其他索引
            "CREATE INDEX IF NOT EXISTS idx_api_logs_user_id ON api_access_logs(user_id);",
            "CREATE INDEX IF NOT EXISTS idx_api_logs_endpoint ON api_access_logs(endpoint);",
            "CREATE INDEX IF NOT EXISTS idx_api_logs_status ON api_access_logs(response_status);",
            "CREATE INDEX IF NOT EXISTS idx_api_logs_created_at ON api_access_logs(created_at);"
        ]
        
        for sql in indexes_sql:
            cursor.execute(sql)

        # 创建唯一约束（修复：添加缺少的约束）
        unique_constraints_sql = [
            "CREATE UNIQUE INDEX IF NOT EXISTS uq_model_table ON comparison_table_rules(model_id, table_id);"
        ]

        for sql in unique_constraints_sql:
            cursor.execute(sql)

        conn.commit()
        conn.close()
        self._log("新数据库结构创建完成")
    
    def _create_default_user(self, user_id: str):
        """创建默认用户"""
        self._log(f"创建默认用户: {user_id}")
        
        conn = sqlite3.connect(self.new_db_path)
        cursor = conn.cursor()
        
        # 插入默认用户
        cursor.execute("""
            INSERT OR IGNORE INTO user_agent (user_id, username, email, password, role)
            VALUES (?, ?, ?, ?, ?)
        """, (user_id, "系统用户", "<EMAIL>", "system_hash", "admin"))
        
        conn.commit()
        conn.close()
    
    def _migrate_existing_data(self, default_user_id: str):
        """迁移现有数据"""
        self._log("开始迁移现有数据")
        
        # 连接旧数据库
        old_conn = sqlite3.connect(self.old_db_path)
        old_cursor = old_conn.cursor()
        
        # 连接新数据库
        new_conn = sqlite3.connect(self.new_db_path)
        new_cursor = new_conn.cursor()
        
        try:
            # 检查旧表是否存在
            old_cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='comparison_results'")
            if not old_cursor.fetchone():
                self._log("旧数据库中未找到comparison_results表")
                return
            
            # 获取旧数据
            old_cursor.execute("SELECT * FROM comparison_results")
            old_records = old_cursor.fetchall()
            
            if not old_records:
                self._log("旧数据库中没有数据需要迁移")
                return
            
            # 获取旧表结构
            old_cursor.execute("PRAGMA table_info(comparison_results)")
            old_columns = [col[1] for col in old_cursor.fetchall()]
            
            self._log(f"找到 {len(old_records)} 条旧记录需要迁移")
            
            # 为每个表创建一个迁移任务
            table_groups = {}
            for record in old_records:
                record_dict = dict(zip(old_columns, record))
                table_name = record_dict.get('table_name', 'unknown')
                
                if table_name not in table_groups:
                    table_groups[table_name] = []
                table_groups[table_name].append(record_dict)
            
            # 创建默认连接配置
            default_conn_id = self._create_default_connections(new_cursor)

            # 创建默认模型
            default_model_id = self._create_default_model(new_cursor, default_conn_id)

            # 为每个表组创建任务并迁移数据
            for table_name, records in table_groups.items():
                # 创建表规则
                rule_id = self._create_table_rule(new_cursor, default_model_id, table_name)

                # 创建迁移任务（修复：使用正确的字段名）
                task_id = str(uuid.uuid4())
                new_cursor.execute("""
                    INSERT INTO comparison_tasks (
                        task_id, user_id, model_id, table_rule_id, task_name,
                        description, status, total_records, diff_records, complete_time
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    task_id, default_user_id, default_model_id, rule_id,
                    f"迁移任务-{table_name}", "从旧数据库迁移的数据",
                    "completed", len(records), len(records), datetime.now()
                ))
                
                # 迁移记录（修复：使用正确的字段名）
                for record in records:
                    new_cursor.execute("""
                        INSERT INTO comparison_results (
                            task_id, table_name, record_key, status, field_name,
                            source_value, target_value, created_at
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        task_id,
                        record.get('table_name', ''),
                        record.get('record_key', ''),
                        record.get('status', ''),
                        record.get('field_name'),
                        record.get('source_value') or record.get('value_a'),  # 兼容旧字段名
                        record.get('target_value') or record.get('value_b'),  # 兼容旧字段名
                        record.get('created_at', datetime.now())
                    ))
                
                self._log(f"迁移表 {table_name}: {len(records)} 条记录")
            
            new_conn.commit()
            self._log("数据迁移完成")
            
        except Exception as e:
            new_conn.rollback()
            raise e
        finally:
            old_conn.close()
            new_conn.close()

    def _create_default_connections(self, cursor) -> int:
        """创建默认连接配置"""
        cursor.execute("""
            INSERT INTO comparison_connections (
                name, type, host, port, username, password, database, status
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, ("默认连接", "sqlite", "localhost", 0, "", "", ":memory:", "active"))

        return cursor.lastrowid

    def _create_default_model(self, cursor, conn_id: int) -> int:
        """创建默认模型"""
        cursor.execute("""
            INSERT INTO comparison_models (
                name, description, source_connid, target_connid, cmp_type, status
            ) VALUES (?, ?, ?, ?, ?, ?)
        """, ("迁移模型", "用于数据迁移的默认模型", conn_id, conn_id, "content", True))

        return cursor.lastrowid

    def _create_table_rule(self, cursor, model_id: int, table_name: str) -> int:
        """创建表规则"""
        cursor.execute("""
            INSERT INTO comparison_table_rules (
                model_id, table_id, table_name, sql_1, sql_2, remark, is_active
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (model_id, table_name, table_name, f"SELECT * FROM {table_name}",
              f"SELECT * FROM {table_name}", f"迁移规则-{table_name}", True))

        return cursor.lastrowid

    def _verify_migration(self):
        """验证迁移结果"""
        self._log("验证迁移结果")
        
        conn = sqlite3.connect(self.new_db_path)
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        expected_tables = [
            'user_agent', 'user_sessions', 'comparison_connections', 'comparison_models',
            'comparison_table_rules', 'comparison_tasks', 'comparison_results', 'api_access_logs'
        ]

        for table in expected_tables:
            if table in tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                self._log(f"表 {table}: {count} 条记录")
            else:
                self._log(f"错误: 表 {table} 不存在")

        # 验证外键约束（修复：添加外键约束验证）
        cursor.execute("PRAGMA foreign_key_check")
        fk_violations = cursor.fetchall()
        if fk_violations:
            self._log(f"警告: 发现 {len(fk_violations)} 个外键约束违反")
            for violation in fk_violations:
                self._log(f"  外键违反: {violation}")
        else:
            self._log("外键约束验证通过")

        conn.close()
    
    def _log(self, message: str):
        """记录迁移日志"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.migration_log.append(log_entry)
        print(log_entry)
    
    def get_migration_log(self) -> List[str]:
        """获取迁移日志"""
        return self.migration_log


def main():
    """主函数"""
    migration = DatabaseMigration(
        old_db_path="diff_results.db",
        new_db_path="service_comparison.db"
    )
    
    success = migration.migrate()
    
    if success:
        print("\n迁移成功完成!")
        print("迁移日志:")
        for log in migration.get_migration_log():
            print(log)
    else:
        print("\n迁移失败!")
        print("错误日志:")
        for log in migration.get_migration_log():
            print(log)


if __name__ == "__main__":
    main()
